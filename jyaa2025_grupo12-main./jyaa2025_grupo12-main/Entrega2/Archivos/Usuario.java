public class Usuario {
    private long id;
    private String Nombre;
    private String Apellido;
    private String Email;
    private Integer DNI;
    private String Telefono;
    private Enum {admin, medico, referente, encuestador} Rol;

    public long getId() {
        return id;
    }

    public String getNombre() {
        return Nombre;
    }

    public void setNombre(String Nombre) {
        this.Nombre = Nombre;
    }

    public String getApellido() {
        return Apellido;
    }

    public void setApellido(String Apellido) {
        this.Apellido = Apellido;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String Email) {
        this.Email = Email;
    }

    public Integer getDNI() {
        return DNI;
    }

    public void setDNI(Integer DNI) {
        this.DNI = DNI;
    }

    public String getTelefono() {
        return Telefono;
    }

    public void setTelefono(String Telefono) {
        this.Telefono = Telefono;
    }

    public Enum {admin, medico, referente, encuestador} getRol() {
        return Rol;
    }

    public void setRol(Enum {admin, medico, referente, encuestador} Rol) {
        this.Rol = Rol;
    }

}