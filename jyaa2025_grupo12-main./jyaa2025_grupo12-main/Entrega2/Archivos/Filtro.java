public class Filtro {
    private long id;
    private String sociales;
    private String enfermedades;
    private Usuario creador;

    public long getId() {
        return id;
    }

    public String getSociales() {
        return sociales;
    }

    public void setSociales(String sociales) {
        this.sociales = sociales;
    }

    public String getEnfermedades() {
        return enfermedades;
    }

    public void setEnfermedades(String enfermedades) {
        this.enfermedades = enfermedades;
    }

    public Usuario getCreador() {
        return creador;
    }

    public void setCreador(Usuario creador) {
        this.creador = creador;
    }

}