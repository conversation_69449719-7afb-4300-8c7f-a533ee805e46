public class EncuestaVivienda{

    private long id;
    private String areaSector;
    private String direccion;
    private String caracterVivienda;
    private Boolean tieneGas;
    private Boolean tieneElectricidad;
    private Boolean tieneAguaPotable;

    public long getId() {
        return id;
    }

    public String getAreaSector() {
        return areaSector;
    }

    public void setAreaSector(String areaSector) {
        this.areaSector = areaSector;
    }

    public String getDireccion() {
        return direccion;
    }

    public void setDireccion(String direccion) {
        this.direccion = direccion;
    }

    public String getCaracterVivienda() {
        return caracterVivienda;
    }

    public void setCaracterVivienda(String caracterVivienda) {
        this.caracterVivienda = caracterVivienda;
    }

    public Boolean getTieneGas() {
        return tieneGas;
    }

    public void setTieneGas(Boolean tieneGas) {
        this.tieneGas = tieneGas;
    }

    public Boolean getTieneElectricidad() {
        return tieneElectricidad;
    }

    public void setTieneElectricidad(Boolean tieneElectricidad) {
        this.tieneElectricidad = tieneElectricidad;
    }

    public Boolean getTieneAguaPotable() {
        return tieneAguaPotable;
    }

    public void setTieneAguaPotable(Boolean tieneAguaPotable) {
        this.tieneAguaPotable = tieneAguaPotable;
    }

}
