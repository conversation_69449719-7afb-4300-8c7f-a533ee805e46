<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="27.0.6">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
    <mxGraphModel grid="1" page="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="zkfFHV4jXpPFQw0GAbJ--0" value="Usuario" style="swimlane;fontStyle=0;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;marginBottom=0;rounded=0;shadow=0;strokeWidth=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="40" y="40" width="160" height="578" as="geometry">
            <mxRectangle x="230" y="140" width="160" height="26" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-29" value="- id: long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="zkfFHV4jXpPFQw0GAbJ--1" value="- Nombre: String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="52" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="zkfFHV4jXpPFQw0GAbJ--2" value="- Apellido: String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rounded=0;shadow=0;html=0;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="78" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="zkfFHV4jXpPFQw0GAbJ--3" value="- Email: String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rounded=0;shadow=0;html=0;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="104" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-0" value="- DNI: Integer" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="130" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-1" value="- Telefono: String" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="160" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-25" value="- Rol: Enum {admin, medico, referente, encuestador}" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="190" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zkfFHV4jXpPFQw0GAbJ--4" value="" style="line;html=1;strokeWidth=1;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="220" width="160" height="20" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-10" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="240" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-109" value="+ getNombre(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="266" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-110" value="+ setNombre(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="292" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-111" value="+ getApellido(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="318" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-112" value="+ setApellido(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="344" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-115" value="+ getEmail(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="370" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-116" value="+ setEmail(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="396" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-0" value="+ getDNI(): Integer" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="422" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-1" value="+ setDNI(Integer) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="448" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-2" value="+ getTelefono(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="474" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-3" value="+ setTelefono(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="500" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-4" value="+ getRol(): Rol" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="526" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-5" value="+ setRol(Rol) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="zkfFHV4jXpPFQw0GAbJ--0">
          <mxGeometry y="552" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-17" value="Personal de Salud" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="90" y="-210" width="206" height="138" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-18" value="- matricula: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-17">
          <mxGeometry y="26" width="206" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-19" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-17">
          <mxGeometry y="52" width="206" height="8" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-89" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-17">
          <mxGeometry y="60" width="206" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-110" value="+ setMatricula(Integer) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-17">
          <mxGeometry y="86" width="206" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-109" value="+ getMatricula(): Integer" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-17">
          <mxGeometry y="112" width="206" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-21" value="OrganizacionSocial" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="440" y="60" width="210" height="468" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-52" value="- nombre: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="26" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-53" value="- domicilio: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="56" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-3" value="- informacionAdicional: String" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="86" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-54" value="- barrio: Barrio" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="116" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-2" value="- actividadPrincipal: String" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="146" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-23" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="176" width="210" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-12" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="184" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-111" value="+ getNombre(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="210" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-112" value="+ setNombre(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="236" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-113" value="+ getDomicilio(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="262" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-114" value="+ setDomicilio(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="288" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-115" value="+ getInformacionAdicional(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="314" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-116" value="+ setInformacionAdicional(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="340" width="210" height="24" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-117" value="+ getBarrio(): Barrio" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="364" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-118" value="+ setBarrio(Barrio) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="390" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-119" value="+ getActividadPrincipal(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="416" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-120" value="+ setActividadPrincipal(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry y="442" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-30" value="Campania" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="1376" y="-510" width="190" height="328" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-4" value="- id: long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="26" width="190" height="26" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-28" value="- nombre: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="52" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-29" value="- fechaInicio: date" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="82" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-30" value="- fechaFin: date" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="112" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-32" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="142" width="190" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-17" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="150" width="190" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-80" value="+ getNombre(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="176" width="190" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-79" value="+ setNombre(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="202" width="190" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-81" value="+ getFechaInicio(): Date" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="228" width="190" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-82" value="+ setFechaInicio(Date) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="254" width="190" height="24" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-84" value="+ setFechaFin(Date) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="278" width="190" height="24" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-83" value="+ getFechaFin(): Date" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gLfXD8LXiHM9PtmFGx3i-30">
          <mxGeometry y="302" width="190" height="26" as="geometry" />
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-26" value="1 .. 1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.526;exitY=1.023;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="S1VNxUZISR1rq-R3uyhT-13" target="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="884" y="380" />
              <mxPoint x="885" y="380" />
            </Array>
            <mxPoint x="941" y="442" as="sourcePoint" />
            <mxPoint x="803" y="586" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-27" value="1 .. 1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.188;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" target="gw41RKY-uf8oOPrNdhjL-30">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="830.0799999999999" y="376" as="sourcePoint" />
            <mxPoint x="450" y="429.38" as="targetPoint" />
            <Array as="points">
              <mxPoint x="830" y="530" />
              <mxPoint x="660" y="530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-28" value="1 .. 1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.807;exitY=1.016;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" target="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="929" y="530" />
              <mxPoint x="1170" y="530" />
            </Array>
            <mxPoint x="929.1200000000001" y="376.41599999999994" as="sourcePoint" />
            <mxPoint x="1153" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.269;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="zkfFHV4jXpPFQw0GAbJ--2" target="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="120" />
              <mxPoint x="330" y="120" />
            </Array>
            <mxPoint x="330" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-8" value="1 .. 1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" connectable="0" vertex="1" parent="FfolL7GzJ_bjC2V0-uSH-31">
          <mxGeometry x="0.1133" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FfolL7GzJ_bjC2V0-uSH-29" target="gLfXD8LXiHM9PtmFGx3i-17">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="260" y="79" />
              <mxPoint x="260" y="-10" />
              <mxPoint x="60" y="-10" />
              <mxPoint x="60" y="-240" />
              <mxPoint x="142" y="-240" />
            </Array>
            <mxPoint x="150" y="-240" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-2" value="1 .. 1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FfolL7GzJ_bjC2V0-uSH-32">
          <mxGeometry x="0.2246" y="2" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="zkfFHV4jXpPFQw0GAbJ--1" target="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="100" />
              <mxPoint x="330" y="100" />
              <mxPoint x="330" y="-10" />
              <mxPoint x="545" y="-10" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-0" value="1 .. 1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FfolL7GzJ_bjC2V0-uSH-33">
          <mxGeometry x="0.2528" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-38" value="1 .. *" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="gLfXD8LXiHM9PtmFGx3i-30" target="gw41RKY-uf8oOPrNdhjL-57">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1421" y="-180" as="sourcePoint" />
            <mxPoint x="1240" y="-210" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1423" y="-150" />
              <mxPoint x="1330" y="-150" />
              <mxPoint x="1330" y="-230" />
              <mxPoint x="1240" y="-230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-39" value="0 .. *" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.322;entryY=-0.022;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.51;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="VRTqllR0CZ-Ujw0B70uj-88">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1220.3199999999997" y="-26.42200000000002" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="1200" y="-8" />
              <mxPoint x="1200" y="121" />
              <mxPoint x="1302" y="121" />
            </Array>
            <mxPoint x="1301.52" y="199.9960000000001" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-41" value="1 .. *" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="H0TaPZWS40dawtQajA68-69" target="gLfXD8LXiHM9PtmFGx3i-9">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1350" y="266.4500000000003" as="sourcePoint" />
            <mxPoint x="1559.97" y="264.4500000000003" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1410" y="270" />
              <mxPoint x="1610" y="270" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-42" value="0 .. *" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="VnMXH5qSTDmzHGev-fR1-2" target="gLfXD8LXiHM9PtmFGx3i-17">
          <mxGeometry x="-0.0001" relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="805" y="5" />
              <mxPoint x="590" y="5" />
              <mxPoint x="590" y="-240" />
              <mxPoint x="245" y="-240" />
              <mxPoint x="245" y="-210" />
            </Array>
            <mxPoint x="210" y="-210" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-45" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.75;entryY=0;entryDx=0;entryDy=0;exitX=-0.001;exitY=0.382;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="H0TaPZWS40dawtQajA68-57" target="gLfXD8LXiHM9PtmFGx3i-21">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="545" y="80" as="targetPoint" />
            <Array as="points">
              <mxPoint x="805" y="-80" />
              <mxPoint x="790" y="-80" />
              <mxPoint x="790" y="40" />
              <mxPoint x="598" y="40" />
              <mxPoint x="598" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-6" value="0 .. *" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FfolL7GzJ_bjC2V0-uSH-45">
          <mxGeometry x="-0.088" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-52" value="0 .. *" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.623;entryY=0.003;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="H0TaPZWS40dawtQajA68-29" target="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry x="0.002" relative="1" as="geometry">
            <mxPoint x="1376" y="-128" as="sourcePoint" />
            <mxPoint x="902.5" y="-100" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-53" value="1 .. 1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="H0TaPZWS40dawtQajA68-29" target="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1566" y="-129" as="sourcePoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-54" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.935;entryY=-0.03;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" target="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="740" y="145" />
              <mxPoint x="740" y="370" />
              <mxPoint x="400" y="370" />
            </Array>
            <mxPoint x="400" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-7" value="1 .. *" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FfolL7GzJ_bjC2V0-uSH-54">
          <mxGeometry x="-0.7736" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FfolL7GzJ_bjC2V0-uSH-57" target="gLfXD8LXiHM9PtmFGx3i-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-3" value="1 .. 1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FfolL7GzJ_bjC2V0-uSH-58">
          <mxGeometry x="0.0726" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-59" value="1 .. *" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="gw41RKY-uf8oOPrNdhjL-72" target="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1850" y="35" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="1790" y="140" />
              <mxPoint x="1690" y="140" />
            </Array>
            <mxPoint x="1670" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-60" value="1 .. *" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" target="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1710" y="16.5" as="sourcePoint" />
            <mxPoint x="1357.52" y="182.6199999999999" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1710" y="-50" />
              <mxPoint x="1520" y="-50" />
              <mxPoint x="1520" y="170" />
              <mxPoint x="1330" y="170" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-62" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.401;exitY=1.008;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" target="gw41RKY-uf8oOPrNdhjL-21">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1455.1899999999998" y="-178.00199999999992" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="1454" y="-178" />
              <mxPoint x="1454" y="140" />
              <mxPoint x="1030" y="140" />
              <mxPoint x="1030" y="250" />
              <mxPoint x="880" y="250" />
            </Array>
            <mxPoint x="884.82" y="305.39000000000004" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-63" value="1 .. *" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" connectable="0" vertex="1" parent="FfolL7GzJ_bjC2V0-uSH-62">
          <mxGeometry x="-0.4747" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" target="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="650" y="230" />
              <mxPoint x="720" y="230" />
              <mxPoint x="720" y="-290" />
              <mxPoint x="845" y="-290" />
            </Array>
            <mxPoint x="650" y="257" as="sourcePoint" />
            <mxPoint x="855" y="-100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-5" value="0 .. *" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="gw41RKY-uf8oOPrNdhjL-4">
          <mxGeometry x="-0.2469" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-12" value="Encuestador" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="250" y="480" width="160" height="306" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-98" value="- genero: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="26" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-99" value="- edad: Integer" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="56" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-100" value="- ocupacion: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="86" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-14" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="116" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-8" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="124" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-11" value="+ getGenero(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="150" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-10" value="+ setGenero(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="176" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-9" value="+ getEdad(): Integer" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="202" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-8" value="+ setEdad(Integer) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="228" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-7" value="+ getOcupacion(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="254" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-13" value="+ setOcupacion(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-12">
          <mxGeometry y="280" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-21" value="Encuesta" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="800" y="290" width="160" height="86" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-2" value="- id: long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-21">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-23" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-21">
          <mxGeometry y="52" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-13" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-21">
          <mxGeometry y="60" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-26" value="Encuesta-Salud" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="780" y="635" width="210" height="400" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-90" value="- edad:Integer" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="26" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-91" value="- nacionalidad: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="56" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-93" value="- tipoComida: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="86" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-92" value="- asistenciaAlimentaria: boolean" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="116" width="210" height="34" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-28" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="150" width="210" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-11" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="158" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-25" value="+ getEdad(): Integer" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="184" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-26" value="+ setEdad(Integer) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="210" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-19" value="+ getNacionalidad(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="236" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-20" value="+ setNacionalidad(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="262" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-21" value="+ getTipoComida(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="288" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-23" value="+ setAsistenciaAlimentaria(boolean) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="314" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-24" value="+ getAsistenciaAlimentaria(): boolean" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="340" width="210" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-22" value="+ setTipoComida(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-26">
          <mxGeometry y="366" width="210" height="34" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-30" value="Encuesta-Social" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="560" y="570" width="200" height="224" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-101" value="- personasObraSocial: Integer" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-30">
          <mxGeometry y="26" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-102" value="- problemasSalud: String[]" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-30">
          <mxGeometry y="56" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-32" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-30">
          <mxGeometry y="86" width="200" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-9" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-30">
          <mxGeometry y="94" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-14" value="+ getPersonasObraSocial(): Integer" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-30">
          <mxGeometry y="120" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-15" value="+ setPersonasObraSocial(Integer) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-30">
          <mxGeometry y="146" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-16" value="+ getProblemasSalud(): String[]" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-30">
          <mxGeometry y="172" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-17" value="+ setProblemasSalud(String[]) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-30">
          <mxGeometry y="198" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-34" value="Encuesta-Vivienda" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="1050" y="590" width="240" height="390" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-42" value="- area/sector: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="26" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-43" value="- direccion: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="56" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-44" value="- caracterVivienda: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="86" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-47" value="- tieneGas: boolean" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="116" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-36" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="146" width="240" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-10" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="154" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-27" value="+ getAreaSector(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="180" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-28" value="+ setAreaSector(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="206" width="240" height="28" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-29" value="+ getDireccion(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="234" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-30" value="+ setDireccion(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="260" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-31" value="+ getCaracterVivienda(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="286" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-32" value="+ setCaracterVivienda(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="312" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-34" value="+ setTieneGas(Integer) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="338" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-33" value="+ getTieneGas(): booleans" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-34">
          <mxGeometry y="364" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-52" value="Reporte" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="805" y="-240" width="160" height="408" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-1" value="- id: long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-56" value="- nombre: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="52" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VnMXH5qSTDmzHGev-fR1-2" value="- captura: imagen" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="82" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-58" value="- publico: boolean" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="112" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-57" value="- creador: Usuario" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="142" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-54" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="172" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-14" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="180" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-104" value="+ getNombre(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="206" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-99" value="+ setNombre(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="232" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-100" value="+ getCaptura(): imagen" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="258" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-103" value="+ setCaptura(imagen) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="284" width="160" height="24" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-102" value="+ getPublico(): boolean" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="308" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-101" value="+ setPublico(boolean) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="334" width="160" height="24" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-108" value="+ getCreador(): Usuario" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="358" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-107" value="+ setCreador(Usuario) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-52">
          <mxGeometry y="384" width="160" height="24" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-57" value="Jornada" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="1120" y="-178" width="160" height="166" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-3" value="- id: long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-57">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-78" value="- fecha: date" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-57">
          <mxGeometry y="52" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-59" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-57">
          <mxGeometry y="82" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-15" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-57">
          <mxGeometry y="90" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-89" value="+ getFecha(): Date" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-57">
          <mxGeometry y="116" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-88" value="+ setFecha(Date) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-57">
          <mxGeometry y="142" width="160" height="24" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-63" value="Zona" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="1250" y="200" width="160" height="258" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-7" value="- id: long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-69" value="- nombre: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry y="52" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-84" value="- informacion: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry y="82" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-65" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry y="122" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-20" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry y="130" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-40" value="+ setNombre(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry y="156" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-39" value="+ getNombre(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry y="182" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-41" value="+ getInformacion(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry y="208" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-42" value="+ setInformacion(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-63">
          <mxGeometry y="234" width="160" height="24" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-72" value="Barrio" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="1710" y="-160" width="200" height="258" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-5" value="- id: long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry y="26" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-40" value="- nombre: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry y="52" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-42" value="- informacionAdicional: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry y="82" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-74" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry y="122" width="200" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-18" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry y="130" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-76" value="+ getNombre(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry y="156" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-75" value="+ setNombre(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry y="182" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-77" value="+ getInformacionAdicional(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry y="208" width="200" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-78" value="+ setInformacionAdicional(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-72">
          <mxGeometry y="234" width="200" height="24" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-85" value="Coordenada" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="1610" y="200" width="160" height="250" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-6" value="- id: long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-9" value="- coorX: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry y="52" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gLfXD8LXiHM9PtmFGx3i-10" value="- coorY: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry y="82" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-87" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry y="112" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-19" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry y="120" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-51" value="+ getCoorX(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry y="146" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-52" value="+ setCoorX(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry y="172" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-53" value="+ getCoorY(): String" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry y="198" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-54" value="+ setCoorY(String) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-85">
          <mxGeometry y="224" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-103" value="Filtro del mapa" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="296" y="-613.5" width="174" height="330" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-0" value="- id: long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="26" width="174" height="26" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-65" value="- sociales: String[]" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="52" width="174" height="30" as="geometry" />
        </mxCell>
        <mxCell id="H0TaPZWS40dawtQajA68-64" value="- enfermedades: String[]" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="82" width="174" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FfolL7GzJ_bjC2V0-uSH-57" value="- creador: Usuario" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="112" width="174" height="30" as="geometry" />
        </mxCell>
        <mxCell id="gw41RKY-uf8oOPrNdhjL-105" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="142" width="174" height="8" as="geometry" />
        </mxCell>
        <mxCell id="S1VNxUZISR1rq-R3uyhT-21" value="+ getId(): long" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="150" width="174" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-97" value="+ getEnfermedades(): String[]" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="176" width="174" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-96" value="+ setEnfermedades(String[]) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="202" width="174" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-94" value="+ getCreador(): Usuario" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="228" width="174" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-95" value="+ setCreador(Usuario) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="254" width="174" height="24" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-91" value="+ getSociales(): String[]" style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="278" width="174" height="26" as="geometry" />
        </mxCell>
        <mxCell id="VRTqllR0CZ-Ujw0B70uj-90" value="+ setSociales(String[]) " style="text;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="gw41RKY-uf8oOPrNdhjL-103">
          <mxGeometry y="304" width="174" height="26" as="geometry" />
        </mxCell>
        <mxCell id="kePyvV3yXqhTnclyZOX4-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" target="gw41RKY-uf8oOPrNdhjL-57">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1376" y="-346.9999999999998" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
