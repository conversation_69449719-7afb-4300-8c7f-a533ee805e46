package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.PreguntaInterface;
import DAO.GenericDAO;
import Modelo.Encuesta.Pregunta;

public class PreguntaDAO extends GenericDAO<Pregunta> implements PreguntaInterface {
    @Inject
    private EntityManager em;
    public PreguntaDAO() {
        super(Pregunta.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
