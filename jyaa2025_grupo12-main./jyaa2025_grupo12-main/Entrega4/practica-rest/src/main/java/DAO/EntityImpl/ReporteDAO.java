package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.ReporteInterface;
import DAO.GenericDAO;
import Modelo.Reporte;

public class ReporteDAO extends GenericDAO<Reporte> implements ReporteInterface {
    @Inject
    private EntityManager em;
    public ReporteDAO() {
        super(Reporte.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
