package DAO.EntityImpl;

import DAO.EntityInterface.HabilitadorInterface;
import DAO.GenericDAO;
import Modelo.Usuarios.Habilitacion;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;

public class HabilitadorDAO extends GenericDAO<Habilitacion> implements HabilitadorInterface {
    @Inject
    private EntityManager em;
    public HabilitadorDAO() {
        super(Habilitacion.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
