package DAO;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityTransaction;
import jakarta.persistence.NoResultException;
import jakarta.persistence.TypedQuery;
import jakarta.transaction.Transactional;

import java.util.List;
import java.util.Optional;

public  abstract class GenericDAO<T> implements GenericDAOInterface<T> {

    private Class<T> entityClass;
    public GenericDAO(Class<T> entityClass) {
        this.entityClass = entityClass;
    }
    @Override
    public List<T> listar() {
        EntityManager em = getEntityManager();
        var transaction = em.getTransaction();
        transaction.begin();
        String jpql = "select t from " + entityClass.getName() + " t ";
        TypedQuery<T> query = em.createQuery(jpql, entityClass);
        List<T> todasEntradas = query.getResultList();
        return todasEntradas;
    }

    @Override
    public T buscarPorId(Long id) {
        try {
            EntityManager em = getEntityManager();
            if (id == 0) {
                return null;
            }
            String jpql = "select t from " + entityClass.getName() + " t where t.id = :id ";
            TypedQuery<T> query = em.createQuery(jpql, entityClass);
            query.setParameter("id", id);
            T unaEntrada = query.getSingleResult();
            return unaEntrada;
        }catch (NoResultException nre){
            return null;
        }
    }


    @Override
    @Transactional
    public T actualizar(T entidad) {
        EntityManager em =getEntityManager();
        EntityTransaction transaction = em.getTransaction();
        transaction.begin();
        em.merge(entidad);
        transaction.commit();
        return entidad;
        }
    @Transactional
    @Override
    public boolean eliminar( Long id) {
        EntityManager em = getEntityManager();
        if (id == 0) {
            return false;
        } else {
            T u = this.buscarPorId(id);
            if (u == null) {
                return false;
            }
            EntityTransaction transaction = em.getTransaction();
            transaction.begin();
            em.remove(u);
            transaction.commit();
            return true;
        }
    }
    @Override
    @Transactional
    public Optional<T> agregar(T u)  {
        EntityManager em = getEntityManager();
        EntityTransaction tx = em.getTransaction();
        tx.begin();
        em.persist(u);
        tx.commit();
        Optional<T> result = Optional.of(u);
       return result;
    }
    public abstract EntityManager getEntityManager();
}
