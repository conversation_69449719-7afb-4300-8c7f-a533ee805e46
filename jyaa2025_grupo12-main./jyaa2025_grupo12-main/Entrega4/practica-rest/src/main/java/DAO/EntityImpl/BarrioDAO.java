package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.BarrioInterface;
import DAO.GenericDAO;
import Modelo.Barrio;


public class BarrioDAO extends GenericDAO<Barrio> implements BarrioInterface {
    @Inject
    private EntityManager em;
    public BarrioDAO() {
        super(Barrio.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
        }
}
