package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.PersonalMedicoInterface;
import DAO.GenericDAO;
import Modelo.Usuarios.PersonalDeSalud;

public class PersonalMedicoDAO extends GenericDAO<PersonalDeSalud> implements PersonalMedicoInterface {
    @Inject
    private EntityManager em;
    public PersonalMedicoDAO() {
        super(PersonalDeSalud.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
