package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.ZonaInterface;
import DAO.GenericDAO;
import Modelo.Zona;

public class ZonaDAO extends GenericDAO<Zona> implements ZonaInterface {
    @Inject
    private EntityManager em;
    public ZonaDAO() {
        super(Zona.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
