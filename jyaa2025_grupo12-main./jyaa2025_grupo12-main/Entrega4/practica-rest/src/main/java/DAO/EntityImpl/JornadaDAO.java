package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.JornadaInterface;
import DAO.GenericDAO;
import Modelo.Jornada;

public class JornadaDAO extends GenericDAO<Jornada> implements JornadaInterface {
    @Inject
    private EntityManager em;
    public JornadaDAO() {
        super(Jornada.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
