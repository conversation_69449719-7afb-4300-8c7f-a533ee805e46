package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.OrganizacionSocialInterface;
import DAO.GenericDAO;
import Modelo.Usuarios.OrganizacionSocial;

public class OrganizacionSocialDAO extends GenericDAO<OrganizacionSocial> implements OrganizacionSocialInterface {
   @Inject
   private EntityManager em;
    public OrganizacionSocialDAO() {
        super(OrganizacionSocial.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
