package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.CoordenadaInterface;
import DAO.GenericDAO;
import Modelo.Coordenada;

public class CoordenadaDAO extends GenericDAO<Coordenada> implements CoordenadaInterface {
    @Inject
    private EntityManager em;
    public CoordenadaDAO() {
        super(Coordenada.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
