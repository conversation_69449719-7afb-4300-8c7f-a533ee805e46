package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.UsuarioInterface;
import DAO.GenericDAO;
import Modelo.Usuarios.Usuario;


public class UsuarioDAO extends GenericDAO<Usuario> implements UsuarioInterface {
    @Inject
    private EntityManager em;
    public UsuarioDAO() {
        super(Usuario.class);

    }
    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
