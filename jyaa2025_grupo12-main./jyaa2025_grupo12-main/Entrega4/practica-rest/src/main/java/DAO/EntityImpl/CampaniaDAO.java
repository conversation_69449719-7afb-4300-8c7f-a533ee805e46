package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.CampaniaInterface;
import DAO.GenericDAO;
import Modelo.Campania;

public class CampaniaDAO extends GenericDAO<Campania> implements CampaniaInterface {
    @Inject
    private EntityManager em;
    public CampaniaDAO() {
        super(Campania.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
