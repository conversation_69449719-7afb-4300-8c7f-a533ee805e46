package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.EncuestaInterface;
import DAO.GenericDAO;
import Modelo.Encuesta.Encuesta;

public class EncuestaDAO extends GenericDAO<Encuesta> implements EncuestaInterface {
    @Inject
    private EntityManager em;
    public EncuestaDAO() {
        super(Encuesta.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
