package DAO;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Named;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import jakarta.persistence.Persistence;
@Named(value="EntityManager")
public class ManagerFactory {
        private static  EntityManager entityManager;
        private ManagerFactory() {
            EntityManagerFactory emf = Persistence.createEntityManagerFactory("miUP");
            entityManager = emf.createEntityManager();
        }
        @Produces
        public static EntityManager getEntityManager() {
            new ManagerFactory();
            return entityManager;
        }
        public static void close(){
            entityManager.close();
        }
    }
