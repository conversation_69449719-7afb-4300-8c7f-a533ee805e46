package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.FiltroMapaInterface;
import DAO.GenericDAO;
import Modelo.FiltroDelMapa;

public class FiltroMapaDAO extends GenericDAO<FiltroDelMapa> implements FiltroMapaInterface {
    @Inject
    private EntityManager em;
    public FiltroMapaDAO() {
        super(FiltroDelMapa.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
