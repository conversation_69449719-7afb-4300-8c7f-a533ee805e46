package DAO.EntityImpl;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import DAO.EntityInterface.EncuestadorInterface;
import DAO.GenericDAO;
import Modelo.Usuarios.Encuestador;

public class EncuestadorDAO extends GenericDAO<Encuestador> implements  EncuestadorInterface {
    @Inject
    private EntityManager em;
    public EncuestadorDAO() {
        super(Encuestador.class);
    }

    @Override
    public EntityManager getEntityManager() {
        return em;
    }
}
