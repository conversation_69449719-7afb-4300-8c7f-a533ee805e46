package configurations;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.servers.Server;
import io.swagger.v3.oas.annotations.servers.ServerVariable;

@OpenAPIDefinition(
        info=@Info(
                title = "Api-Rest-Entrega-4",
                version = "1.0.0",
                description = "Esta es una forma de dcumentar y presentar una forma amigable de mostrar el servicio" +
                        "hasta ahora",
                contact = @Contact(
                        name = "<PERSON><PERSON><PERSON><PERSON>",
                        email = "<EMAIL>",
                        url = "https://gitlab.catedras.linti.unlp.edu.ar/jyaa_2025/jyaa2025_grupo12"
                )
        ),
        servers = {
                @Server(
                        description = "Servidor local de desarrollo",
                        url = "http://localhost:8080/practica_rest_war/"
                ),
                @Server(
                        description = "Servidor de producción",
                        url = "",
                        variables = {
                                @ServerVariable(
                                        name = "",
                                        defaultValue = "",
                                        description = ""
                                )
                        }
                )
        }
)
public class OpenApiConfig {
}
