package Services;

import Controller.GenericABM;
import DAO.EntityImpl.CampaniaDAO;
import Modelo.Campania;
import jakarta.inject.Inject;

import java.util.List;

public class CampaniaService implements GenericABM<Campania> {
    @Inject
    private CampaniaDAO campaniaDAO;
    @Override
    public List<Campania> listar() {
        return campaniaDAO.listar();
    }

    @Override
    public Campania agregar(Campania elemento) {
        return campaniaDAO.agregar(elemento).orElse(null);
    }

    @Override
    public Campania actualizar(Campania elemento) {
        return campaniaDAO.actualizar(elemento);
    }

    @Override
    public boolean eliminar(Long id) {
        return campaniaDAO.eliminar(id);
    }

    @Override
    public Campania obtener(Long id) {
        return campaniaDAO.buscarPorId(id);
    }
}
