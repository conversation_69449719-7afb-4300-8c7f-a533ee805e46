package Services;

import Controller.GenericABM;
import DAO.EntityImpl.ZonaDAO;
import Modelo.Zona;
import jakarta.inject.Inject;

import java.util.List;

public class ZonaService implements GenericABM<Zona> {
    @Inject
    ZonaDAO zonaDAO;
    @Override
    public List<Zona> listar() {
        return zonaDAO.listar();
    }

    @Override
    public Zona agregar(Zona elemento) {
       return zonaDAO.agregar(elemento).orElse(null);
    }

    @Override
    public Zona actualizar(Zona elemento) {
        return zonaDAO.actualizar(elemento);
    }

    @Override
    public boolean eliminar(Long id) {
        return zonaDAO.eliminar(id);
    }

    @Override
    public Zona obtener(Long id) {
        return zonaDAO.buscarPorId(id);
    }
}
