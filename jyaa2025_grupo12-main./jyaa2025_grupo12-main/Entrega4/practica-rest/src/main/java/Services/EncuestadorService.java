package Services;

import Controller.GenericABM;
import DAO.EntityImpl.EncuestadorDAO;
import Modelo.Usuarios.Encuestador;
import jakarta.inject.Inject;

import java.util.List;
import java.util.Optional;

public class EncuestadorService implements GenericABM<Encuestador> {
    @Inject
    private EncuestadorDAO encuestadorDAO;

    @Override
    public List<Encuestador> listar() {
        return encuestadorDAO.listar();
    }

    public Encuestador agregar(Encuestador encuestador){
        Optional<Encuestador> opt = encuestadorDAO.agregar(encuestador);
        return opt.orElseThrow();
    }

    @Override
    public Encuestador actualizar(Encuestador elemento) {
        return encuestadorDAO.actualizar(elemento);
    }

    @Override
    public boolean eliminar(Long id) {
        return encuestadorDAO.eliminar(id);
    }

    @Override
    public Encuestador obtener(Long id) {
        return encuestadorDAO.buscarPorId(id);
    }
}
