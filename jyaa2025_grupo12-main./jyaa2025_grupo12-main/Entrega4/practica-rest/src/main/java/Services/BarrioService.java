package Services;

import Controller.GenericABM;
import DAO.EntityImpl.BarrioDAO;
import Modelo.Barrio;
import jakarta.inject.Inject;
import org.glassfish.jersey.process.internal.RequestScoped;

import java.util.List;

@RequestScoped
public class  BarrioService implements GenericABM<Barrio> {
    @Inject
    private BarrioDAO  barrioDAO;
    @Override
    public List<Barrio> listar() {
        return barrioDAO.listar();
    }

    @Override
    public Barrio agregar(Barrio elemento) {
        return barrioDAO.agregar(elemento).orElse(null);
    }

    @Override
    public Barrio actualizar(Barrio elemento) {
        return barrioDAO.actualizar(elemento);
    }

    @Override
    public boolean eliminar(Long id) {
        return barrioDAO.eliminar(id);
    }

    @Override
    public Barrio obtener(Long id) {
        return barrioDAO.buscarPorId(id);
    }
}
