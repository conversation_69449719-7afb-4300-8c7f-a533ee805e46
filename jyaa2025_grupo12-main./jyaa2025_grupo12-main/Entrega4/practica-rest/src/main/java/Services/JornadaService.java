package Services;

import Controller.GenericABM;
import DAO.EntityImpl.JornadaDAO;
import Modelo.Jornada;
import jakarta.inject.Inject;

import java.util.List;

public class JornadaService implements GenericABM<Jornada> {
    @Inject
    private JornadaDAO jornadaDAO;
    @Override
    public List<Jornada> listar() {
        return jornadaDAO.listar();
    }

    @Override
    public Jornada agregar(Jornada elemento) {
        return jornadaDAO.agregar(elemento).orElse(null);
    }

    @Override
    public Jornada actualizar(Jornada elemento) {
        return jornadaDAO.actualizar(elemento);
    }

    @Override
    public boolean eliminar(Long id) {
        return jornadaDAO.eliminar(id);
    }

    @Override
    public Jornada obtener(Long id) {
        return jornadaDAO.buscarPorId(id);
    }
}
