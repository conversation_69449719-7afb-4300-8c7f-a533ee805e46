package Services;

import Controller.GenericABM;
import DAO.EntityImpl.OrganizacionSocialDAO;
import Modelo.Usuarios.OrganizacionSocial;
import jakarta.inject.Inject;

import java.util.List;
import java.util.Optional;

public class OrganizacionSocialService implements GenericABM<OrganizacionSocial> {
    @Inject
    private OrganizacionSocialDAO organizacionSocialDAO;

    @Override
    public List<OrganizacionSocial> listar() {
        return organizacionSocialDAO.listar();
    }

    public OrganizacionSocial agregar(OrganizacionSocial organizacionSocial) {
        Optional<OrganizacionSocial> opt = organizacionSocialDAO.agregar(organizacionSocial);
        return opt.orElseThrow(() -> new RuntimeException("No se encontro el registro"));
    }

    @Override
    public OrganizacionSocial actualizar(OrganizacionSocial elemento) {
        return organizacionSocialDAO.actualizar(elemento);
    }

    @Override
    public boolean eliminar(Long elemento) {
        return organizacionSocialDAO.eliminar(elemento);
    }

    @Override
    public OrganizacionSocial obtener(Long id) {
        return organizacionSocialDAO.buscarPorId(id);
    }
}
