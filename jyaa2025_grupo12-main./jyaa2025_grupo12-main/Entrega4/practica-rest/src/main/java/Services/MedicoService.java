package Services;

import Controller.GenericABM;
import DAO.EntityImpl.PersonalMedicoDAO;
import Modelo.Usuarios.PersonalDeSalud;
import jakarta.inject.Inject;

import java.util.List;
import java.util.Optional;

public class MedicoService implements GenericABM<PersonalDeSalud> {
    @Inject
    private PersonalMedicoDAO personalMedicoDAO;

    @Override
    public List<PersonalDeSalud> listar() {
        return personalMedicoDAO.listar();
    }

    public PersonalDeSalud agregar(PersonalDeSalud personalDeSalud) {
        Optional<PersonalDeSalud> opt =  personalMedicoDAO.agregar(personalDeSalud);
        return opt.orElse(null);
    }

    @Override
    public PersonalDeSalud actualizar(PersonalDeSalud elemento) {
        return personalMedicoDAO.actualizar(elemento);
    }

    @Override
    public boolean eliminar(Long id) {
        return personalMedicoDAO.eliminar(id);
    }

    @Override
    public PersonalDeSalud obtener(Long id) {
        return personalMedicoDAO.buscarPorId(id);
    }
}
