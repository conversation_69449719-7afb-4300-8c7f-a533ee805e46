package Services;

import Controller.GenericABM;
import DAO.EntityImpl.*;
import Modelo.Usuarios.Encuestador;
import Modelo.Usuarios.Habilitacion;
import Modelo.Usuarios.PersonalDeSalud;
import Modelo.Usuarios.Usuario;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Response;

import java.util.List;

@Named
public class UsuarioService implements GenericABM<Usuario> {
    @Inject
    private UsuarioDAO usuarioDAO;
    @Inject
    private OrganizacionSocialDAO organizacionSocialDAO;
    @Inject
    private EncuestadorDAO encuestadorDAO;
    @Inject
    private PersonalMedicoDAO personalDeSaludDAO;


    @Override
    public List<Usuario> listar() {
        return usuarioDAO.listar();
    }

    @Override
    public Usuario agregar(Usuario elemento) {
        return usuarioDAO.agregar(elemento).orElse(null);
    }

    @Override
    public Usuario actualizar(Usuario elemento) {
        return usuarioDAO.actualizar(elemento);
    }

    @Override
    public  boolean eliminar(Long id) {
        return usuarioDAO.eliminar(id);
    }

    @Override
    public Usuario obtener(Long id) {
        return usuarioDAO.buscarPorId(id);
    }
}
