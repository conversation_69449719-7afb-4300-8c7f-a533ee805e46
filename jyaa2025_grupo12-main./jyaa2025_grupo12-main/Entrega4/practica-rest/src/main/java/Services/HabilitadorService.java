package Services;

import Controller.GenericABM;
import DAO.EntityImpl.HabilitadorDAO;
import DAO.EntityImpl.UsuarioDAO;
import Modelo.Usuarios.Habilitacion;
import Modelo.Usuarios.Usuario;
import jakarta.inject.Inject;

import java.util.List;
import java.util.Optional;

public class HabilitadorService implements GenericABM<Habilitacion> {
    @Inject
    private HabilitadorDAO habilitadorDAO;

    @Override
    public List<Habilitacion> listar() {
        return habilitadorDAO.listar();
    }

    @Override
    public Habilitacion agregar(Habilitacion elemento) {
        return habilitadorDAO.agregar(elemento).orElse(null);
    }

    @Override
    public Habilitacion actualizar(Habilitacion elemento) {
        return habilitadorDAO.actualizar(elemento);
    }

    @Override
    public boolean eliminar(Long id) {
        return habilitadorDAO.eliminar(id);
    }

    @Override
    public Habilitacion obtener(Long id) {
        return habilitadorDAO.buscarPorId(id);
    }
}
