package Controller;

import Modelo.Usuarios.Encuestador;
import Services.EncuestadorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Path("/encuestador")
@Tag(name = "Ejemplo Encuestador Controller", description = "Controller que nos permite hacer operaciones CRUD sobre las Encuestadores", externalDocs = @io.swagger.v3.oas.annotations.ExternalDocumentation(description = "Documentación completa", url = "does not exist"))
public class EncuestadorController {
    @Inject
    private EncuestadorService encuestadorService;


    @GET
    public List<Encuestador> listar() {
        return encuestadorService.listar();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(description = "Este endpoint nos permite crear un Barrio, toma como body un JSON y retona el mismo JSON " +
            "pero con un ID de creación",
            requestBody = @RequestBody(description = "Un nuevo barrio en formato JSON",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            examples = {@ExampleObject(
                                    name="Ejemplo 1",
                                    summary = "Esto es un ejemplo 1",
                                    value = """
                        {
                            "nombre":"El Hueco",
                            "informacionAdicional":"Es un barrio muy loco",
                            "zonas":[
                                {
                                    "nombre":"Zona 1",
                                    "informacion":"Información de la zona 1",
                                    "coordenadas":[
                                        {
                                            "coorX":"32.1",
                                            "coorY":"12.4"
                                        }
                                    ]
                        
                                }
                            ]
                        
                        }
                        """
                            )}
                    )))

    public Encuestador agregar(Encuestador encuestador) {
        return encuestadorService.agregar(encuestador);
    }

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(description = "Este endpoint permite actualizar un barrio", requestBody = @RequestBody(description = "Actualizar barrio en formato JSON", required = true, content = @Content(mediaType = "application/json", examples = {@ExampleObject(name = "Barrio para actualizar", summary = "Barrio para actualizar", value = """
            {
                "id": 1,
                "genero":"Masculino",
                "ocupacion":"estudiante",
                "edad":25
                "usuario":{
                        "id":3,
                         "nombre":"Sebastian",
                         "apellido":"Perro",
                         "email":"<EMAIL>",
                         "DNI":3929302,
                         "rol":"ENCUESTADOR",
                         "telefono":"2249438221",
                         "habilitacion":{
                            "activo":false,
                            "verificado":false
                         }
                        }
            }
            """)})), responses = {@ApiResponse(responseCode = "204", description = "Actualización exitosa"), @ApiResponse(responseCode = "400", description = "Error de validación")})

    public Encuestador actualizar(Encuestador elemento) {
        return encuestadorService.actualizar(elemento);
    }


    @DELETE
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(description = "Este endpoint nos permite eliminar un encuestador, toma como parámetros de la cabecera de la" + "petición el id, de la entrada que se quiere eliminar", parameters = @Parameter(name = "Id encuestador"))

    public boolean eliminar(@QueryParam("id") Long id) {
        return encuestadorService.eliminar(id);
    }

    @GET
    @Path("/one")
    @Produces(MediaType.APPLICATION_JSON)
    public Encuestador obtener(@QueryParam("id") Long id) {
        return encuestadorService.obtener(id);
    }
}
