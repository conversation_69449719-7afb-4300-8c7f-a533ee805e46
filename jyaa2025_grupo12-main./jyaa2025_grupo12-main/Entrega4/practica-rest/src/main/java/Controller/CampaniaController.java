package Controller;

import Modelo.Campania;
import Services.CampaniaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;

import java.util.List;
@Path("/campanias")
@Tag(
        name = "Ejemplo Campania Controller",
        description = "Controller que nos permite hacer operaciones CRUD sobre las Campanias",
        externalDocs = @io.swagger.v3.oas.annotations.ExternalDocumentation(
                description = "Documentación completa",
                url = "does not exist"
        )
)

public class CampaniaController implements GenericABM<Campania>{
    @Inject
    private CampaniaService campaniaService;
    @Override
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(description = "Este endpoint nos permite crear una Campania, toma como body un JSON y retona el mismo JSON " +
            "pero con un ID de creación",
            requestBody = @RequestBody(description = "Una nueva Campania en formato JSON",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            examples = {@ExampleObject(
                                    name="Ejemplo 1",
                                    summary = "Esto es un ejemplo 1",
                                    value = """
                                            {
                                               "id": 0,
                                               {
                                                //Campaña
                                                "nombre":"Camapaña 1",
                                                "fechaInicio": "2025-06-15 00:00:00",
                                                "fechaFin": "2025-06-15 00:00:00",
                                                "barrio": {
                                                   //Barrio
                                                    "nombre":"Barrio 1",
                                                    "informacionAdicional": "Informacion barrio 1",
                                                    "zonas":[
                                                        {
                                                           "nombre":"Zona 1",
                                                           "informacion": "Información de la zona 1",
                                                           "coordenadas":[
                                                            {
                                                                "coorX":"-32.3",
                                                                "coorY":"29.2"
                                                            }
                                                           ]
                                                        }
                                                    ]
                                                },
                                                "jornadas":[
                                                   {
                                                    "fecha":"2025-06-15 00:00:00",
                                                    "zonaList":[
                                                        {
                                                            "nombre":"Zona 2",
                                                            "informacion":"Información de la zona 2",
                                                            "coordenadas":[
                                                                {
                                                                    "coorX":"-32.1",
                                                                    "coorY":"29.4"
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                   }
                                                ],
                                                "encuestadores":[
                                                    {
                                                        "genero":"Masculino",
                                                        "ocupacion":"Estudiante",
                                                        "edad":25,
                                                        "usuario":{
                                                            "nombre":"Marcelo",
                                                            "apellido":"Cri",
                                                            "email":"<EMAIL>",
                                                            "DNI":30493821,
                                                            "telefono":"2249183482",
                                                            "rol":"ENCUESTADOR"
                                                        }
                                                    }
                                                ],
                                                "reportes":[
                                                    {
                                                        "nombre":"Reporte 1",
                                                        "imagen":"data://",
                                                        "publico":true,
                                                        "creador":{
                                                            "matricula":2388332,
                                                            "usuario":{
                                                                "nombre":"Creador",
                                                                "apellido":"Nuevo",
                                                                "email":"<EMAIL>",
                                                                "DNI":39320384,
                                                                "telefono":"2291384321",
                                                                "rol":"MEDICO"
                                                            }
                                                        },
                                                        "solicitantes":[
                                                            {
                                                               "nombre":"Ayudante anonimos",
                                                                "domicilio":"Domicilo nuevo 123",
                                                                "informacionAdicional":"Es una organización que ayuda",
                                                                "actividadPrincipal":"Hace lo que puede",
                                                                "usuario":{
                                                                    "nombre":"trabajador 1",
                                                                    "apellido":"de la empresa",
                                                                    "DNI":39420123,
                                                                    "email":"<EMAIL>",
                                                                    "telefono":"238302383",
                                                                    "rol":"REFERENTE"
                                                                },
                                                                "barrio":{
                                                                    "nombre":"Barrio 5",
                                                                    "informacionAdicional":"Es una barrio",
                                                                    "zonas":[
                                                                        {
                                                                            "nombre":"Zona 3",
                                                            "informacion":"Información de la zona 3",
                                                            "coordenadas":[
                                                                {
                                                                    "coorX":"-42.1",
                                                                    "coorY":"39.4"
                                                                }
                                                            ]
                                                                        }
                                                                    ]
                                                                }
                                                            }
                                                        ]
                                                    }
                                                ],
                                                "encuestas":[
                                                    {
                                                    "preguntas":[
                                                        {
                                                          "numero":1,
                                                            "pregunta":"Alguien esta enfermo?",
                                                            "respuesta":"Nadie",
                                                            "respondio":true
                                                        }
                                                    ]
                                                    }
                                                ]
                                            }
                                    """
                            )}
                    )))
    public Campania agregar(Campania elemento) {
        return campaniaService.agregar(elemento);
    }

    @Override
    @GET
    @Produces(MediaType.APPLICATION_JSON)

    @Operation(
            description = "Este endpoint permite actualizar una campania",
            requestBody = @RequestBody(
                    description = "Actualizar campania en formato JSON",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            examples = {@ExampleObject(
                                    name="Campania para actualizar",
                                    summary = "Campania para actualizar",
                                    value = """
                                            {
                                             {
                                                 //Campaña
                                                 "id":1,
                                                 "nombre":"Camapaña 1",
                                                 "fechaInicio": "2025-06-16 00:00:00",
                                                 "fechaFin": "2025-06-16 00:00:00",
                                                 "barrio": {
                                                    //Barrio
                                                     "id":1,
                                                     "nombre":"Barrio 1",
                                                     "informacionAdicional": "Informacion barrio 1",
                                                     "zonas":[
                                                         {
                                                             "id":1,
                                                            "nombre":"Zona 1",
                                                            "informacion": "Información de la zona 1",
                                                            "coordenadas":[
                                                             {
                                                                 "id":1,
                                                                 "coorX":"-32.3",
                                                                 "coorY":"29.2"
                                                             }
                                                            ]\s
                                                         }
                                                     ]
                                                 },
                                                 "jornadas":[
                                                    {
                                                     "id":1,
                                                     "fecha":"2025-06-15 00:00:00",
                                                     "zonaList":[
                                                         {
                                                             "id":2,
                                                             "nombre":"Zona 2",
                                                             "informacion":"Información de la zona 2",
                                                             "coordenadas":[
                                                                 {
                                                                     "id":2,
                                                                     "coorX":"-32.1",
                                                                     "coorY":"29.4"
                                                                 }
                                                             ]
                                                         }
                                                     ]
                                                    }
                                                 ],
                                                 "encuestadores":[
                                                     {
                                                         "id":1,
                                                         "genero":"Masculino",
                                                         "ocupacion":"Estudiante",
                                                         "edad":25,
                                                         "usuario":{
                                                             "id":1,
                                                             "nombre":"Marcelo",
                                                             "apellido":"Cri",
                                                             "email":"<EMAIL>",
                                                             "DNI":30493821,
                                                             "telefono":"2249183482",
                                                             "rol":"ENCUESTADOR"
                                                         }
                                                     }
                                                 ],
                                                 "reportes":[
                                                     {
                                                         "id":1,
                                                         "nombre":"Reporte 1",
                                                         "imagen":"data://",
                                                         "publico":true,
                                                         "creador":{
                                                             "id":1,
                                                             "matricula":2388332,
                                                             "usuario":{
                                                                 "id":2,
                                                                 "nombre":"Creador",
                                                                 "apellido":"Nuevo",
                                                                 "email":"<EMAIL>",
                                                                 "DNI":39320384,
                                                                 "telefono":"2291384321",
                                                                 "rol":"MEDICO"
                                            
                                                             }
                                                         },
                                                         "solicitantes":[
                                                             {
                                                                 "id":1,
                                                                "nombre":"Ayudante anonimos",
                                                                 "domicilio":"Domicilo nuevo 123",
                                                                 "informacionAdicional":"Es una organización que ayuda",
                                                                 "actividadPrincipal":"Hace lo que puede",
                                                                 "usuario":{
                                                                     "id":3,
                                                                     "nombre":"trabajador 1",
                                                                     "apellido":"de la empresa",
                                                                     "DNI":39420123,
                                                                     "email":"<EMAIL>",
                                                                     "telefono":"238302383",
                                                                     "rol":"REFERENTE"
                                            
                                                                 },
                                                                 "barrio":{
                                                                     "id":2,
                                                                     "nombre":"Barrio 5",
                                                                     "informacionAdicional":"Es una barrio",
                                                                     "zonas":[
                                                                         {
                                                                             "id":3,
                                                                             "nombre":"Zona 3",
                                                                             "informacion":"Información de la zona 3",
                                                                             "coordenadas":[
                                                                                 {
                                                                                     "id":3,
                                                                                     "coorX":"-42.1",
                                                                                     "coorY":"39.4"
                                                                                 }
                                                                             ]
                                                                         }
                                                                     ]                      
                                                                 }
                                                             }
                                                         ]
                                            
                                                     }
                                            
                                                 ],
                                                 "encuestas":[
                                                     {
                                                         "id":1,
                                                     "preguntas":[
                                                         {
                                                             "id":1,
                                                             "numero":1,
                                                             "pregunta":"Quién sos?",
                                                             "respuesta":"Nadie",
                                                             "respondio":true
                                                         }
                                                     ]
                                                     }
                                                 ]
                                             }
                                            """
                            )}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "204",description = "Actualización exitosa"),
                    @ApiResponse(responseCode = "400", description = "Error de validación")
            }
    )


    public List<Campania> listar() {
        return campaniaService.listar();
    }

    @Override
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Campania actualizar(Campania elemento) {
        return campaniaService.actualizar(elemento);
    }

    @Override
    @DELETE
    @Operation(description = "Este endpoint nos permite eliminar una campania, toma como parámetros de la cabecera de la" +
            "petición el id, de la entrada que se quiere eliminar",
            parameters = @Parameter(name="Id campania"))

    public boolean eliminar(@QueryParam("id") Long id) {
        return campaniaService.eliminar(id);
    }

    @Override
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/one")
    public Campania obtener(@QueryParam("id") Long id) {
        return campaniaService.obtener(id);
    }
}
