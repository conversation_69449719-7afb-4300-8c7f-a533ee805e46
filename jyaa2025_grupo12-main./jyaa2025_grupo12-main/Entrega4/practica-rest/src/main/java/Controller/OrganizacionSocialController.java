package Controller;

import Modelo.Usuarios.OrganizacionSocial;
import Services.OrganizacionSocialService;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;

import java.util.List;

@Path("/organizacion_social")
public class OrganizacionSocialController implements GenericABM<OrganizacionSocial> {
    @Inject
    private OrganizacionSocialService organizacionSocialService;

    @Override
    @GET
    public List<OrganizacionSocial> listar() {
        return organizacionSocialService.listar();
    }

    @POST
    public OrganizacionSocial agregar(OrganizacionSocial organizacionSocial) {
        return organizacionSocialService.agregar(organizacionSocial);
    }

    @Override
    @PUT
    public OrganizacionSocial actualizar(OrganizacionSocial elemento) {
        return organizacionSocialService.actualizar(elemento);
    }

    @Override
    @DELETE
    public boolean eliminar(@QueryParam("id") Long id) {
        return organizacionSocialService.eliminar(id);
    }

    @Override
    @GET
    @Path("/one")
    public OrganizacionSocial obtener(@QueryParam("id") Long id) {
        return organizacionSocialService.obtener(id);
    }
}
