package Controller;

import Modelo.Barrio;
import Services.BarrioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;

import java.util.List;
@Path("/barrios")
@Tag(
        name = "Ejemplo Barrio Controller",
        description = "Controller que nos permite hacer operaciones CRUD sobre los Barrios",
        externalDocs = @io.swagger.v3.oas.annotations.ExternalDocumentation(
                description = "Documentación completa",
                url = "does not exist"
        )
)
public class BarrioController implements GenericABM<Barrio>{
    @Inject
    private BarrioService barrioService;
    @Override
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public List<Barrio> listar() {
        return barrioService.listar();
    }

    @Override
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(description = "Este endpoint nos permite crear un Barrio, toma como body un JSON y retona el mismo JSON " +
            "pero con un ID de creación",
    requestBody = @RequestBody(description = "Un nuevo barrio en formato JSON",
    required = true,
    content = @Content(
        mediaType = "application/json",
        examples = {@ExampleObject(
                name="Ejemplo 1",
                summary = "Esto es un ejemplo 1",
                value = """
                        {
                            "nombre":"El Hueco",
                            "informacionAdicional":"Es un barrio muy loco",
                            "zonas":[
                                {
                                    "nombre":"Zona 1",
                                    "informacion":"Información de la zona 1",
                                    "coordenadas":[
                                        {
                                            "coorX":"32.1",
                                            "coorY":"12.4"
                                        }
                                    ]
                        
                                }
                            ]
                        
                        }
                        """
        )}
    )))
    public Barrio agregar(Barrio elemento) {
        return barrioService.agregar(elemento);
    }

    @Override
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(
            description = "Este endpoint permite actualizar un barrio",
            requestBody = @RequestBody(
                    description = "Actualizar barrio en formato JSON",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            examples = {@ExampleObject(
                                    name="Barrio para actualizar",
                                    summary = "Barrio para actualizar",
                                    value = """
                                            {
                                                "id":3,
                                                "nombre":"El Manzana",
                                                "informacionAdicional":"Es un barrio muy loco",
                                                "zonas":[
                                                    {
                                                        "id":4
                                                    },
                                                    {
                                                        "id":5
                                                    }
                                                ]
                                            }
                                            """
                            )}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "204",description = "Actualización exitosa"),
                    @ApiResponse(responseCode = "400", description = "Error de validación")
            }
    )
    public Barrio actualizar(Barrio elemento) {
        return barrioService.actualizar(elemento);
    }

    @Override
    @DELETE
    @Operation(description = "Este endpoint nos permite eliminar una barrio, toma como parámetros de la cabecera de la" +
            "petición el id, de la entrada que se quiere eliminar",
    parameters = @Parameter(name="Id barrio"))
    public boolean eliminar(@QueryParam("id") Long id) {
        return barrioService.eliminar(id);
    }

    @Override
    @GET
    @Path("/one")
    public Barrio obtener(@QueryParam("id") Long id) {
        return barrioService.obtener(id);
    }
}
