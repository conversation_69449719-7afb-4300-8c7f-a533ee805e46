package Controller;

import Modelo.Usuarios.Usuario;
import Services.UsuarioService;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.GenericEntity;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;

@Path("/usuario")
public class UsuarioController implements GenericABM<Usuario>{
    @Inject
    private UsuarioService usuarioService;
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public List<Usuario> listar() {
        return  usuarioService.listar();
    }
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Usuario agregar(Usuario usuario) {
        return usuarioService.agregar(usuario);

    }
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Usuario actualizar(Usuario usuario) {
        return usuarioService.actualizar(usuario);
    }
    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public boolean eliminar(@QueryParam("id") Long id) {
        return usuarioService.eliminar(id);
    }
    @GET
    @Path("/one")
    @Produces(MediaType.APPLICATION_JSON)
    public Usuario obtener(@QueryParam("id") Long id){
        return usuarioService.obtener(id);
    }
}
