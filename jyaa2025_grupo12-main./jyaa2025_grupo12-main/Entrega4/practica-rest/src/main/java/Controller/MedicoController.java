package Controller;

import Modelo.Usuarios.PersonalDeSalud;
import Services.MedicoService;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Path("/medico")
public class MedicoController implements GenericABM<PersonalDeSalud> {
    @Inject
    public MedicoService medicoService;

    @Override
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public List<PersonalDeSalud> listar() {
        return medicoService.listar();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public PersonalDeSalud agregar(PersonalDeSalud personalDeSalud) {
        return medicoService.agregar(personalDeSalud);
    }

    @Override
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public PersonalDeSalud actualizar(PersonalDeSalud elemento) {
        return medicoService.actualizar(elemento);
    }

    @Override
    @DELETE
    public boolean eliminar(@QueryParam("id") Long id) {
        return medicoService.eliminar(id);
    }

    @Override
    @GET
    @Path("/one")
    @Produces(MediaType.APPLICATION_JSON)
    public PersonalDeSalud obtener(@QueryParam("id") Long id) {
        return medicoService.obtener(id);
    }
}
