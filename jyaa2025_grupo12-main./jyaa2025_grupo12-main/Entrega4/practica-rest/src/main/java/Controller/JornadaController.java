package Controller;

import Modelo.Jornada;
import Services.JornadaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Path("/jornada")
@Tag(
        name = "Ejemplo Jornada Controller",
        description = "Controller que nos permite hacer operaciones CRUD sobre los Jornada",
        externalDocs = @io.swagger.v3.oas.annotations.ExternalDocumentation(
                description = "Documentación completa",
                url = "does not exist"
        )
)
public class JornadaController implements GenericABM<Jornada> {
    @Inject
    private JornadaService jornadaService;
    @Override
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public List<Jornada> listar() {
        return jornadaService.listar();
    }

    @Override
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Jornada actualizar(Jornada elemento) {
        return jornadaService.actualizar(elemento);
    }

    @Override
    @DELETE
    public boolean eliminar(@QueryParam("id") Long id) {
        return jornadaService.eliminar(id);
    }

    @Override
    @GET
    @Path("/one")
    @Produces(MediaType.APPLICATION_JSON)
    public Jornada obtener(@QueryParam("id") Long id) {
        return jornadaService.obtener(id);
    }

    @Override
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(description = "Este endpoint nos permite crear una jornada, toma como body un JSON y retona el mismo JSON " +
            "pero con un ID de creación",
            requestBody = @RequestBody(description = "Una nueva jornada en formato JSON",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            examples = {@ExampleObject(
                                    name="Ejemplo 1",
                                    summary = "Esto es un ejemplo 1",
                                    value = """
                        {
                            "fecha":"2025-06-14 00:00:00",
                            "zonaList":[
                                {
                                 "nombre":"Zona 1",
                                 "informacion":"Es información de la zona 1",
                                 "coordenadas":[
                                    {
                                        "coorX":"39",
                                        "coorY":"20"
                                    }
                                    ]
                                    },
                                   {
                                    "nombre": "Zona 2",
                                    "informacion": "Es información de la zona 2",
                                    "coordenadas":[
                                    {
                                        "coorX":"34",
                                        "coorY":"25"
                                    }
                                 ]
                                 }
                           ]
                        
                        }
                        """
                            )}
                    )))

    public Jornada agregar(Jornada elemento) {
        return jornadaService.agregar(elemento);
    }
}
