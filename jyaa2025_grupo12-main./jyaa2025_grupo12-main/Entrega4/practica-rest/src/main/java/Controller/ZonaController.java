package Controller;

import Modelo.Zona;
import Services.ZonaService;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;

import java.util.List;
@Path("/zonas")
public class ZonaController implements GenericABM<Zona> {
    @Inject
    private ZonaService zonaService;
    @Override
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public List<Zona> listar() {
        return zonaService.listar();
    }

    @Override
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Zona agregar(Zona elemento) {
        return zonaService.agregar(elemento);
    }

    @Override
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Zona actualizar(Zona elemento) {
        return zonaService.actualizar(elemento);
    }

    @Override
    @DELETE
    public boolean eliminar(@QueryParam("id") Long id) {
        return zonaService.eliminar(id);
    }

    @Override
    @GET
    @Path("/one")
    @Produces(MediaType.APPLICATION_JSON)
    public Zona obtener(@QueryParam("id") Long id) {
        return zonaService.obtener(id);
    }
}
