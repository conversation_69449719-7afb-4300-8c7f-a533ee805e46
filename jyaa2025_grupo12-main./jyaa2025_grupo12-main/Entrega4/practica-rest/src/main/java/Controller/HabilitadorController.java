package Controller;

import Modelo.Usuarios.Habilitacion;
import Modelo.Usuarios.Usuario;
import Services.HabilitadorService;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;

@Path("/usuario/habilitar")
public class HabilitadorController implements GenericABM<Habilitacion> {
    @Inject
    private HabilitadorService  habilitadorService;

    @Override
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public List<Habilitacion> listar() {
        return habilitadorService.listar();
    }

    @Override
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Habilitacion agregar(Habilitacion elemento) {
        return habilitadorService.agregar(elemento);
    }

    @Override
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Habilitacion actualizar(Habilitacion elemento) {
        return habilitadorService.actualizar(elemento);
    }

    @Override
    @DELETE
    public boolean eliminar(@QueryParam("id") Long id) {
        return habilitadorService.eliminar(id);
    }

    @Override
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/one")
    public Habilitacion obtener(@QueryParam("id") Long id) {
        return habilitadorService.obtener(id);
    }
}
