package Modelo;

import Modelo.Encuesta.Encuesta;
import Modelo.Usuarios.Encuestador;
import jakarta.json.bind.annotation.JsonbDateFormat;
import jakarta.persistence.*;

import java.util.Date;
import java.util.List;

@Entity
public class Campania {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String nombre;
    @JsonbDateFormat("yyyy-MM-dd HH:mm:ss")
    private Date fechaInicio;
    @JsonbDateFormat("yyyy-MM-dd HH:mm:ss")
    private Date fechaFin;
    @OneToOne(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
    private Barrio barrio;
    @OneToMany(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
    private List<Jornada> jornadas;
    @OneToMany(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
    private List<Encuestador>  encuestadores;
    @OneToMany(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
    private List<Reporte> reportes;
    @OneToMany(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
    private List<Encuesta>  encuestas;

    public Long getId() {
        return id;
    }

    public List<Encuesta> getEncuestas() {
        return encuestas;
    }

    public void setEncuestas(List<Encuesta> encuestas) {
        this.encuestas = encuestas;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Date getFechaInicio() {
        return fechaInicio;
    }

    public void setFechaInicio(Date fechaInicio) {
        this.fechaInicio = fechaInicio;
    }

    public Date getFechaFin() {
        return fechaFin;
    }

    public void setFechaFin(Date fechaFin) {
        this.fechaFin = fechaFin;
    }

    public Barrio getBarrio() {
        return barrio;
    }

    public void setBarrio(Barrio barrio) {
        this.barrio = barrio;
    }

    public List<Jornada> getJornadas() {
        return jornadas;
    }

    public void setJornadas(List<Jornada> jornadas) {
        this.jornadas = jornadas;
    }

    public List<Encuestador> getEncuestadores() {
        return encuestadores;
    }

    public void setEncuestadores(List<Encuestador> encuestas) {
        this.encuestadores = encuestas;
    }

    public List<Reporte> getReportes() {
        return reportes;
    }

    public void setReportes(List<Reporte> reportes) {
        this.reportes = reportes;
    }
}
