package Modelo.Usuarios;
import jakarta.json.bind.annotation.JsonbTransient;
import jakarta.persistence.*;

import java.io.Serializable;


@Entity
public class Usuario implements Serializable {
    public enum Rol {
        ADMINISTRADOR, MEDICO, REFERENTE, ENCUESTADOR
    }
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String nombre;
    private String apellido;
    private String email;
    private String DNI;
    private String telefono;
    private Rol rol;
    @OneToOne(cascade = CascadeType.ALL,fetch = FetchType.EAGER)
    private Habilitacion habilitacion;
    public void setId(Long id) {
        this.id = id;
    }
    public Long getId() {
        return id;
    }
    public Habilitacion getHabilitacion() {
        return habilitacion;
    }

    public void setHabilitacion(Habilitacion habilitacion) {
        this.habilitacion = habilitacion;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public String getApellido() {
        return apellido;
    }

    public void setApellido(String apellido) {
        this.apellido = apellido;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDNI() {
        return DNI;
    }

    public void setDNI(String DNI) {
        this.DNI = DNI;
    }

    public String getTelefono() {
        return telefono;
    }

    public void setTelefono(String telefono) {
        this.telefono = telefono;
    }

    public Rol getRol() {
        return rol;
    }

    public void setRol(Rol rol) {
        this.rol = rol;
    }
}
