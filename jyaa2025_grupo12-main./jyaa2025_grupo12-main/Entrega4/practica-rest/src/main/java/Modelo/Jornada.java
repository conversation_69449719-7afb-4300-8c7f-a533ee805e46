package Modelo;

import jakarta.json.bind.annotation.JsonbDateFormat;
import jakarta.persistence.*;

import java.util.Date;
import java.util.List;

@Entity
public class Jornada {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @JsonbDateFormat("yyyy-MM-dd HH:mm:ss")
    private Date fecha;
    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    private List<Zona> zonaList;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getFecha() {
        return fecha;
    }

    public void setFecha(Date fecha) {
        this.fecha = fecha;
    }

    public List<Zona> getZonaList() {
        return zonaList;
    }

    public void setZonaList(List<Zona> zonaList) {
        this.zonaList = zonaList;
    }
}
