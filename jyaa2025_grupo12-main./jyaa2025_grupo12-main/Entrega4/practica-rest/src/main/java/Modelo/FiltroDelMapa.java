package Modelo;

import Modelo.Usuarios.Usuario;
import jakarta.persistence.*;

import java.util.List;

@Entity
public class FiltroDelMapa {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private List<Enfermedad> enfermedades;
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private List<Social> sociales;
    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Usuario usuario;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    // Esto tiene que ser una entidad
    public List<Social> getSociales() {
        return sociales;
    }

    public void setSociales(List<Social> sociales) {
        this.sociales = sociales;
    }
    // Esto tiene que ser una entidad
    public List<Enfermedad> getEnfermedades() {
        return enfermedades;
    }

    public void setEnfermedades(List<Enfermedad> enfermedades) {
        this.enfermedades = enfermedades;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }
}
