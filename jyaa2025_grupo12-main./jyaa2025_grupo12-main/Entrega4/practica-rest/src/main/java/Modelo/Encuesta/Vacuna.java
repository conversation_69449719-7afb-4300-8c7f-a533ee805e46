package Modelo.Encuesta;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

@Entity
public class Vacuna {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String vacuna;
    private Integer numeroAplicación;
    private Integer numeroTotalDeAplicaciones;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVacuna() {
        return vacuna;
    }

    public void setVacuna(String vacuna) {
        this.vacuna = vacuna;
    }

    public Integer getNumeroAplicación() {
        return numeroAplicación;
    }

    public void setNumeroAplicación(Integer numeroAplicación) {
        this.numeroAplicación = numeroAplicación;
    }

    public Integer getNumeroTotalDeAplicaciones() {
        return numeroTotalDeAplicaciones;
    }

    public void setNumeroTotalDeAplicaciones(Integer numeroTotalDeAplicaciones) {
        this.numeroTotalDeAplicaciones = numeroTotalDeAplicaciones;
    }
}
