package jyaa.trabajo_practico_3.Modelo;

import jakarta.persistence.*;

import java.util.Date;
import java.util.List;

@Entity
public class Campania {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String nombre;
    private Date fechaInicio;
    private Date fechaFin;
    @OneToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
    private Barrio barrio;
    @OneToMany(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
    private List<Jornada> jornada;
    @OneToMany(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
    private List<Encuestador>  encuestadores;
    @OneToMany(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
    private List<Reporte> reportes;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Date getFechaInicio() {
        return fechaInicio;
    }

    public void setFechaInicio(Date fechaInicio) {
        this.fechaInicio = fechaInicio;
    }

    public Date getFechaFin() {
        return fechaFin;
    }

    public void setFechaFin(Date fechaFin) {
        this.fechaFin = fechaFin;
    }

    public Barrio getBarrio() {
        return barrio;
    }

    public void setBarrio(Barrio barrio) {
        this.barrio = barrio;
    }

    public List<Jornada> getJornada() {
        return jornada;
    }

    public void setJornada(List<Jornada> jornada) {
        this.jornada = jornada;
    }

    public List<Encuestador> getEncuestadores() {
        return encuestadores;
    }

    public void setEncuestadores(List<Encuestador> encuestas) {
        this.encuestadores = encuestas;
    }

    public List<Reporte> getReportes() {
        return reportes;
    }

    public void setReportes(List<Reporte> reportes) {
        this.reportes = reportes;
    }
}
