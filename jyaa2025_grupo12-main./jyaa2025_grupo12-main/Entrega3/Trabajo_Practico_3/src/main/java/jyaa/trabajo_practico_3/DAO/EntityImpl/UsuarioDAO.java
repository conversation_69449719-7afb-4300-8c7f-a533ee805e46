package jyaa.trabajo_practico_3.DAO.EntityImpl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityTransaction;
import jakarta.persistence.NoResultException;
import jakarta.persistence.TypedQuery;
import jyaa.trabajo_practico_3.DAO.EntityInterface.UsuarioInterface;
import jyaa.trabajo_practico_3.DAO.GenericDAO;
import jyaa.trabajo_practico_3.Modelo.Usuario;
import jyaa.trabajo_practico_3.Util.ManagerFactory;

import java.util.List;
import java.util.Optional;

public class UsuarioDAO extends GenericDAO<Usuario> implements UsuarioInterface {
    private PersonalMedicoDAO personalMedicoDAO;
    private EncuestadorDAO encuestadorDAO;
    private OrganizacionSocialDAO organizacionSocialDAO;

    public UsuarioDAO() {
        super(Usuario.class);
        personalMedicoDAO = new PersonalMedicoDAO();
        encuestadorDAO = new EncuestadorDAO();
        organizacionSocialDAO = new OrganizacionSocialDAO();
    }

    public EntityManager getEntityManager() {
        return ManagerFactory.getEntityManager();
    }

    private Optional<Usuario> checkRepeatUsuario(String email) {
        EntityManager em = getEntityManager();
        String query = "FROM Usuario u WHERE u.email = :email";
        TypedQuery<Usuario> q = em.createQuery(query, Usuario.class);
        q.setParameter("email", email);
        List<Usuario> u = q.getResultList();
        if (u.isEmpty()) {
            return null;
        } else {
            return Optional.of(u.get(0));
        }
    }

    @Override
    public Optional<Usuario> agregar(Usuario usuario) {
        //Agregar usuario con checkeo de repetidos
        try {
            EntityManager em = getEntityManager();
            Optional<Usuario> opt = checkRepeatUsuario(usuario.getEmail());
            ;
            if (opt != null) {
                return opt;
            } else {
                if (usuario.getRol().equals(Usuario.Rol.ADMINISTRADOR)) {
                    EntityTransaction tx = em.getTransaction();
                    tx.begin();
                    em.persist(usuario);
                    tx.commit();
                }
                opt = Optional.of(usuario);
                return opt;
            }
        }catch (NoResultException e) {
            e.printStackTrace();
            return null;
        }
    }
}
