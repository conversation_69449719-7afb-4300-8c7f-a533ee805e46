package jyaa.trabajo_practico_3.Servlets;

import jakarta.servlet.ServletConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jyaa.trabajo_practico_3.DAO.EntityImpl.EncuestaDAO;
import jyaa.trabajo_practico_3.Modelo.Encuesta.Encuesta;
import jyaa.trabajo_practico_3.Modelo.Encuesta.Pregunta;
import jyaa.trabajo_practico_3.Modelo.Encuesta.Seccion;
import jyaa.trabajo_practico_3.Modelo.Encuesta.Sustancia;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name="ejemplo_pregunta",value="/pregunta")
public class EjemploEncuesta extends HttpServlet {
    private EncuestaDAO encuestaDAO;
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        encuestaDAO = new EncuestaDAO();
    }
    public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // Una encuesta
        Encuesta encuesta = new Encuesta();
        // Preguntas relacionadas a la encuesta
        List<Pregunta> preguntas = new ArrayList<>();
        //Pregunta 1 - Pregunta Simple
        Pregunta pregunta1 = new Pregunta();
        pregunta1.setPregunta("¿Cómo te llamas?");
        pregunta1.setNumero(1);
        pregunta1.setRespuesta("Sofia");
        pregunta1.setRespondio(true);
        // Sección correspondiente a la pregunta 1
        Seccion seccion1 = new Seccion();
        seccion1.setSeccion("Preguntas Personales");
        pregunta1.setSeccion(seccion1);
        preguntas.add(pregunta1);
        //Pregunta 2
        Pregunta  pregunta2 = new Pregunta();
        pregunta2.setRespondio(true);
        pregunta2.setNumero(2);
        pregunta2.setPregunta("¿Qué sustancias consumis?");
        // Sección correspondiente a la pregunta 2
        Seccion seccion2 = new Seccion();
        seccion2.setSeccion("Preguntas Médicas");
        pregunta2.setSeccion(seccion2);
        // Pregunta que contenga de respuesta una lista de sustancias
        List<Sustancia> sustancias = new ArrayList<>();
        Sustancia sustancia1 = new Sustancia();
        sustancia1.setSustancia("Marihuana");
        sustancias.add(sustancia1);
        Sustancia sustancia2 = new Sustancia();
        sustancia2.setSustancia("Cocaina");
        sustancias.add(sustancia2);
        pregunta2.setSustancias(sustancias);
        preguntas.add(pregunta2);
        encuesta.setPreguntas(preguntas);
        encuestaDAO.agregar(encuesta);
    }
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        //Asumiendo que primero se utilizo el doPost(), en otro caso esto tiraría un error.
        List<Encuesta> encuestas = encuestaDAO.listar();
        for(Encuesta encuesta : encuestas) {
            System.out.println("El número de la encuesta es: "+encuesta.getId());
            for(Pregunta pregunta : encuesta.getPreguntas()) {
                System.out.println("Pregunta: "+pregunta.getId()+" "+pregunta.getPregunta());
            }
        }
    }
    public void doPut(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // Para modificar una encuesta hay que modificar una pregunta
        Encuesta encuesta = encuestaDAO.buscarPorId(1);
        List<Pregunta> preguntas = encuesta.getPreguntas();
        preguntas.get(0).setPregunta("Pregunta modificada da la encuesta 1");
        encuestaDAO.actualizar(encuesta);
    }
    public void doDelete(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Encuesta encuesta = encuestaDAO.buscarPorId(1);
        encuestaDAO.eliminar(encuesta.getId());
    }
}
