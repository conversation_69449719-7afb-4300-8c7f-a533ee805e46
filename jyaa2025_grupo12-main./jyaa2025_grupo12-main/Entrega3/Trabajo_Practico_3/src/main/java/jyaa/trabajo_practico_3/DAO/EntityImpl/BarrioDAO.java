package jyaa.trabajo_practico_3.DAO.EntityImpl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityTransaction;
import jyaa.trabajo_practico_3.DAO.EntityInterface.BarrioInterface;
import jyaa.trabajo_practico_3.DAO.GenericDAO;
import jyaa.trabajo_practico_3.Modelo.Barrio;
import jyaa.trabajo_practico_3.Modelo.Zona;
import jyaa.trabajo_practico_3.Util.ManagerFactory;

import java.util.List;
import java.util.Optional;

public class BarrioDAO extends GenericDAO<Barrio> implements BarrioInterface {
    public BarrioDAO() {
        super(Barrio.class);
    }
    public Optional<Barrio> agregar(Barrio b, List<Zona> zonas){
        EntityManager em = ManagerFactory.getEntityManager();
        EntityTransaction tx = em.getTransaction();
        tx.begin();
        b.setZonas(zonas);
        em.persist(b);
        tx.commit();
        return Optional.of(b);
    }
}
