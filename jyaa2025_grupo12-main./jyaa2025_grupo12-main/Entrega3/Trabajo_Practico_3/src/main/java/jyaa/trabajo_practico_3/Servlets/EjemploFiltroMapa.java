package jyaa.trabajo_practico_3.Servlets;


import jakarta.servlet.ServletConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jyaa.trabajo_practico_3.DAO.EntityImpl.FiltroMapaDAO;
import jyaa.trabajo_practico_3.DAO.EntityImpl.UsuarioDAO;
import jyaa.trabajo_practico_3.Modelo.Enfermedad;
import jyaa.trabajo_practico_3.Modelo.FiltroDelMapa;
import jyaa.trabajo_practico_3.Modelo.Social;
import jyaa.trabajo_practico_3.Modelo.Usuario;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name="ejemplo_filtro_mapa", value="/ejemploF")
public class EjemploFiltroMapa extends HttpServlet {
    private FiltroMapaDAO filtroMapaDAO = new FiltroMapaDAO();
    private UsuarioDAO usuarioDAO = new UsuarioDAO();
    public void init(ServletConfig  servletConfig) throws ServletException {
        super.init(servletConfig);
        filtroMapaDAO = new FiltroMapaDAO();
        usuarioDAO = new UsuarioDAO();
    }
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Usuario usuario = new Usuario();
        usuario.setNombre("Elias");
        usuario.setApellido("Orbe");
        usuario.setEmail("<EMAIL>");
        usuario.setDNI("40393210");
        usuario.setTelefono("2241596232");
        usuario.setRol(Usuario.Rol.MEDICO);
        FiltroDelMapa  filtroDelMapa = new FiltroDelMapa();
        Enfermedad enfermedad1 = new Enfermedad();
        enfermedad1.setEnfermedad("Gripe");
        Enfermedad enfermedad2 = new Enfermedad();
        enfermedad2.setEnfermedad("Sida");
        Enfermedad enfermedad3 = new Enfermedad();
        enfermedad3.setEnfermedad("Lepra");
        List<Enfermedad> enfermedades = new ArrayList<>();
        enfermedades.add(enfermedad1);
        enfermedades.add(enfermedad2);
        enfermedades.add(enfermedad3);
        filtroDelMapa.setEnfermedades(enfermedades);
        Social social1 =  new Social();
        social1.setNombre("Falta de agua");
        Social social2 = new Social();
        social2.setNombre("Falta de cloacas");
        List<Social> sociales = new ArrayList<>();
        sociales.add(social1);
        sociales.add(social2);
        filtroDelMapa.setSociales(sociales);
        filtroDelMapa.setUsuario(usuario);
        filtroMapaDAO.agregar(filtroDelMapa);
    }
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        List<FiltroDelMapa> filtroMapas = filtroMapaDAO.listar();
        for (FiltroDelMapa filtroMapa : filtroMapas) {
            System.out.println("Número de filtro: "+filtroMapa.getId());
            System.out.println("enefermeadad 1 " + filtroMapa.getEnfermedades().get(0).getEnfermedad());
            System.out.println("Social 1 "+ filtroMapa.getSociales().get(0).getNombre());
            System.out.println("usuario " + filtroMapa.getUsuario().getNombre());
        }
    }
    public void doPut(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        FiltroDelMapa fm = filtroMapaDAO.buscarPorId(1);
        List<Enfermedad> enfermedades = fm.getEnfermedades();
        enfermedades.get(0).setEnfermedad("Enfermedad 1 modificada");
        enfermedades.get(1).setEnfermedad("Enfermedad 2 modificada");
        filtroMapaDAO.actualizar(fm);
    }
    public void doDelete(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        FiltroDelMapa fm = filtroMapaDAO.buscarPorId(1);
        filtroMapaDAO.eliminar(fm.getId());
    }
}
