package jyaa.trabajo_practico_3.Servlets;

import jakarta.servlet.ServletConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jyaa.trabajo_practico_3.DAO.EntityImpl.EncuestadorDAO;
import jyaa.trabajo_practico_3.DAO.EntityImpl.OrganizacionSocialDAO;
import jyaa.trabajo_practico_3.DAO.EntityImpl.PersonalMedicoDAO;
import jyaa.trabajo_practico_3.DAO.EntityImpl.UsuarioDAO;
import jyaa.trabajo_practico_3.Modelo.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@WebServlet(name="ejemplo_usuario",value="/ejemploU")
public class EjmeploUsuario extends HttpServlet {
    private UsuarioDAO usuarioDAO;
    private PersonalMedicoDAO personalMedicoDAO;
    private OrganizacionSocialDAO organizacionSocialDAO;
    private EncuestadorDAO encuestadorDAO;
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        usuarioDAO = new UsuarioDAO();
        personalMedicoDAO = new PersonalMedicoDAO();
        organizacionSocialDAO = new OrganizacionSocialDAO();
        encuestadorDAO = new EncuestadorDAO();
    }

    public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        //Usuario administrador
        Usuario usuario = new Usuario();
        usuario.setNombre("Sebastián");
        usuario.setApellido("Butcovich");
        usuario.setDNI("45673892");
        usuario.setEmail("<EMAIL>");
        usuario.setTelefono("+45673892");
        usuario.setRol(Usuario.Rol.ADMINISTRADOR);
        usuarioDAO.agregar(usuario);
        Usuario usuario2 = new Usuario();
        usuario2.setNombre("Juan");
        usuario2.setApellido("LL");
        usuario2.setDNI("45673895");
        usuario2.setEmail("<EMAIL>");
        usuario2.setTelefono("+45673832");
        usuario2.setRol(Usuario.Rol.MEDICO);
        PersonalDeSalud ps = new PersonalDeSalud();
        ps.setUsuario(usuario2);
        ps.setMatricula((long)2394820);
        ps.setUsuario(usuario2);
        personalMedicoDAO.agregar(ps);
        Coordenada coordenada = new Coordenada();
        coordenada.setCoorX("29");
        coordenada.setCoorY("21");
        Coordenada coordenada2 = new Coordenada();
        coordenada2.setCoorX("32");
        coordenada2.setCoorY("27");
        Coordenada coordenada3 = new Coordenada();
        coordenada3.setCoorX("53");
        coordenada3.setCoorY("21");
        //Zona
        Zona zona = new Zona();
        zona.setNombre("La matanza");
        zona.setInformacion("Lugar peligroso");
        List<Coordenada> coordenadas = new ArrayList<Coordenada>();
        coordenadas.add(coordenada2);
        coordenadas.add(coordenada3);
        zona.setCoordenadas(coordenadas);
        //Barrio
        Barrio barrio = new Barrio();
        barrio.setNombre("Ni idea");
        barrio.setInformacionAdicional("Nadie sabe donde queda");
        List<Zona> zonas = new  ArrayList<>();
        zonas.add(zona);
        barrio.setZonas(zonas);
        Usuario usuario3 = new Usuario();
        usuario3.setNombre("Roberto");
        usuario3.setApellido("Sanchez");
        usuario3.setDNI("43673893");
        usuario3.setEmail("<EMAIL>");
        usuario3.setTelefono("+54673893");
        usuario3.setRol(Usuario.Rol.REFERENTE);
        OrganizacionSocial organizacionSocial = new OrganizacionSocial();
        organizacionSocial.setNombre("Comida para todos");
        organizacionSocial.setDomicilio("Calle falsa 123");
        organizacionSocial.setActividadPrincipal("Llevar comida al barrio");
        organizacionSocial.setInformacionAdicional("Fundada 1959");
        organizacionSocial.setUsuario(usuario3);
        organizacionSocial.setBarrio(barrio);
        organizacionSocialDAO.agregar(organizacionSocial);
        Usuario usuario4 = new Usuario();
        usuario4.setNombre("Robo");
        usuario4.setApellido("Marquelios");
        usuario4.setDNI("33673893");
        usuario4.setEmail("<EMAIL>");
        usuario4.setTelefono("+54373893");
        usuario4.setRol(Usuario.Rol.ENCUESTADOR);
        Encuestador encuestador = new Encuestador();
        encuestador.setEdad(25);
        encuestador.setOcupacion("Estudiante");
        encuestador.setGenero("Masculino");
        encuestador.setUsuario(usuario4);
        encuestadorDAO.agregar(encuestador);
    }
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        List<Usuario> usuarios = usuarioDAO.listar();
        for (Usuario usuario : usuarios) {
            System.out.println(usuario.getNombre());
            System.out.println(usuario.getApellido());
            System.out.println(usuario.getDNI());
            System.out.println(usuario.getEmail());
            System.out.println(usuario.getTelefono());
            System.out.println(usuario.getRol());
        }
    }
    public void doPut(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Usuario usuario = usuarioDAO.buscarPorId(1);
        usuario.setApellido("apellido_modificado");
        usuario.setNombre("nombre_modificado");
        usuarioDAO.actualizar(usuario);
    }
    public void doDelete(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Usuario usuario = usuarioDAO.buscarPorId(1);
        usuarioDAO.eliminar(usuario.getId());
    }
}
