package jyaa.trabajo_practico_3.Servlets;


import jakarta.servlet.ServletConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jyaa.trabajo_practico_3.DAO.EntityImpl.ReporteDAO;
import jyaa.trabajo_practico_3.Modelo.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
@WebServlet(name="ejemplo_reporte",value="/ejemploR")
public class EjemploReporte extends HttpServlet {
    private ReporteDAO reporteDAO;
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        reporteDAO = new ReporteDAO();
    }
    public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Usuario usuario = new Usuario();
        usuario.setRol(Usuario.Rol.MEDICO);
        usuario.setDNI("23940123");
        usuario.setTelefono("2245491232");
        usuario.setNombre("Marcelo");
        usuario.setApellido("Roba facil");
        PersonalDeSalud psd = new PersonalDeSalud();
        psd.setMatricula((long)38201283);
        psd.setUsuario(usuario);
        Usuario usuario3 = new Usuario();
        usuario3.setNombre("Roberto");
        usuario3.setApellido("Sanchez");
        usuario3.setDNI("43673893");
        usuario3.setEmail("<EMAIL>");
        usuario3.setTelefono("+54673893");
        usuario3.setRol(Usuario.Rol.REFERENTE);
        Coordenada coordenada = new Coordenada();
        coordenada.setCoorX("29");
        coordenada.setCoorY("21");
        Coordenada coordenada2 = new Coordenada();
        coordenada2.setCoorX("32");
        coordenada2.setCoorY("27");
        Coordenada coordenada3 = new Coordenada();
        coordenada3.setCoorX("53");
        coordenada3.setCoorY("21");
        //Zona
        Zona zona = new Zona();
        zona.setNombre("La matanza");
        zona.setInformacion("Lugar peligroso");
        List<Coordenada> coordenadas = new ArrayList<Coordenada>();
        coordenadas.add(coordenada2);
        coordenadas.add(coordenada3);
        zona.setCoordenadas(coordenadas);
        //Barrio
        Barrio barrio = new Barrio();
        barrio.setNombre("Ni idea");
        barrio.setInformacionAdicional("Nadie sabe donde queda");
        List<Zona> zonas = new  ArrayList<>();
        zonas.add(zona);
        barrio.setZonas(zonas);
        OrganizacionSocial organizacionSocial = new OrganizacionSocial();
        organizacionSocial.setNombre("Comida para todos");
        organizacionSocial.setDomicilio("Calle falsa 123");
        organizacionSocial.setActividadPrincipal("Llevar comida al barrio");
        organizacionSocial.setInformacionAdicional("Fundada 1959");
        organizacionSocial.setUsuario(usuario3);
        organizacionSocial.setBarrio(barrio);
        Reporte reporte = new Reporte();
        reporte.setNombre("Reporte espiritual");
        reporte.setImagen("data:image://base64");
        reporte.setPublico(true);
        reporte.setCreador(psd);
        List<OrganizacionSocial> list = new ArrayList<OrganizacionSocial>();
        list.add(organizacionSocial);
        reporte.setSolicitantes(list);
        reporteDAO.agregar(reporte);
    }
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        List<Reporte> reporte = reporteDAO.listar();
        for(Reporte r : reporte){
            System.out.println("Nombre del reporte "+r.getNombre());
            System.out.println("Imagen: "+r.getImagen());
            System.out.println("Número de matricula de personal medico: "+r.getCreador().getMatricula());
            System.out.println("Nombre de la organización social que solicito el reporte: "+r.getSolicitantes().get(0).getNombre());
        }
    }
    public void doPut(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Reporte reporte = reporteDAO.buscarPorId(1);
        reporte.setNombre("Reporte Modificado");
        reporteDAO.actualizar(reporte);
        //Volver a llamar a la función doGet() con listar
    }
    public void doDelete(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Reporte reporte = reporteDAO.buscarPorId(1);
        reporteDAO.eliminar(reporte.getId());
    }
}
