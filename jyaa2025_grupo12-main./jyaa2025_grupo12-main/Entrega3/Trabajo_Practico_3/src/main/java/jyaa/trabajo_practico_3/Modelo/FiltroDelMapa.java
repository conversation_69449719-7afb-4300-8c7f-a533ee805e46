package jyaa.trabajo_practico_3.Modelo;

import jakarta.persistence.*;

import java.util.List;

@Entity
public class FiltroDelMapa {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    private List<Social> sociales;
    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    private List<Enfermedad> enfermedades;
    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private Usuario usuario;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<Social> getSociales() {
        return sociales;
    }

    public void setSociales(List<Social> sociales) {
        this.sociales = sociales;
    }
    // Esto tiene que ser una entidad
    public List<Enfermedad> getEnfermedades() {
        return enfermedades;
    }

    public void setEnfermedades(List<Enfermedad> enfermedades) {
        this.enfermedades = enfermedades;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }
}
