package jyaa.trabajo_practico_3.Servlets;


import jakarta.servlet.ServletConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jyaa.trabajo_practico_3.DAO.EntityImpl.CampaniaDAO;
import jyaa.trabajo_practico_3.Modelo.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@WebServlet(name="campania",value = "/ejemploC")
public class EjemploCampania extends HttpServlet{
    private CampaniaDAO campaniaDAO;
    public void init(ServletConfig config) throws ServletException {
        super.init(config);
        campaniaDAO = new CampaniaDAO();
    }
    public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        List<Campania> campanias = campaniaDAO.listar();
        for(Campania campania : campanias) {
            System.out.println(campania.getNombre()+" "+ campania.getFechaInicio()+" "+ campania.getFechaFin());
        }
    }
    public void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        List<Encuestador> listEncuestadores = new ArrayList<>();
        Encuestador encuestador = new Encuestador();
        Usuario usuario = new Usuario();
        usuario.setNombre("usuario encuestador");
        usuario.setApellido("usuario encuestador2");
        usuario.setEmail("email encuestador");
        usuario.setRol(Usuario.Rol.ENCUESTADOR);
        usuario.setDNI("39348392");
        usuario.setTelefono("123456789");
        encuestador.setUsuario(usuario);
        encuestador.setGenero("femenino");
        encuestador.setOcupacion("ocupacion");
        encuestador.setEdad(19);
        encuestador.setUsuario(usuario);
        listEncuestadores.add(encuestador);
        Campania campania = new Campania();
        Barrio barrio = new Barrio();
        List<Zona> zonas = new ArrayList<>();
        //Zona 1 con sus coordenadas
        Zona z1 = new Zona();
        Coordenada c1z1 = new Coordenada();
        c1z1.setCoorX("30");
        c1z1.setCoorY("25");
        Coordenada c2z1 = new Coordenada();
        c2z1.setCoorX("40");
        c2z1.setCoorY("20");
        List<Coordenada> coordenadasZ1 = new ArrayList<>();
        coordenadasZ1.add(c1z1);
        coordenadasZ1.add(c2z1);
        z1.setNombre("La movida");
        z1.setInformacion("Es una zona buena");
        z1.setCoordenadas(coordenadasZ1);
        zonas.add(z1);
        //Barrio 1
        barrio.setZonas(zonas);
        barrio.setNombre("Barrio");
        barrio.setInformacionAdicional("Información adicional");
        //Jornada
        Jornada jornada = new Jornada();
        jornada.setFecha(new Date());
        List<Jornada> jornadas = new ArrayList<>();
        jornadas.add(jornada);
        //Médico
        List<Reporte> reportes = new ArrayList<>();
        Usuario usuario4 = new Usuario();
        usuario4.setRol(Usuario.Rol.MEDICO);
        usuario4.setDNI("23940123");
        usuario4.setTelefono("2245491232");
        usuario4.setNombre("Marcelo");
        usuario4.setApellido("Roba facil");
        PersonalDeSalud psd = new PersonalDeSalud();
        psd.setMatricula((long)38201283);
        psd.setUsuario(usuario4);
        //Referente
        Usuario usuario3 = new Usuario();
        usuario3.setNombre("Roberto");
        usuario3.setApellido("Sanchez");
        usuario3.setDNI("43673893");
        usuario3.setEmail("<EMAIL>");
        usuario3.setTelefono("+54673893");
        usuario3.setRol(Usuario.Rol.REFERENTE);
        Coordenada coordenada = new Coordenada();
        coordenada.setCoorX("29");
        coordenada.setCoorY("21");
        Coordenada coordenada2 = new Coordenada();
        coordenada2.setCoorX("32");
        coordenada2.setCoorY("27");
        Coordenada coordenada3 = new Coordenada();
        coordenada3.setCoorX("53");
        coordenada3.setCoorY("21");
        //Zona
        Zona zona = new Zona();
        zona.setNombre("La matanza");
        zona.setInformacion("Lugar peligroso");
        List<Coordenada> coordenadas = new ArrayList<Coordenada>();
        coordenadas.add(coordenada);
        coordenadas.add(coordenada2);
        coordenadas.add(coordenada3);
        zona.setCoordenadas(coordenadas);
        //Organización social
        zonas.add(zona);
        barrio.setZonas(zonas);
        OrganizacionSocial organizacionSocial = new OrganizacionSocial();
        organizacionSocial.setNombre("Comida para todos");
        organizacionSocial.setDomicilio("Calle falsa 123");
        organizacionSocial.setActividadPrincipal("Llevar comida al barrio");
        organizacionSocial.setInformacionAdicional("Fundada 1959");
        organizacionSocial.setUsuario(usuario3);
        organizacionSocial.setBarrio(barrio);
        // Reporte
        Reporte reporte = new Reporte();
        reporte.setNombre("Reporte espiritual");
        reporte.setImagen("data:image://base64");
        reporte.setPublico(true);
        reporte.setCreador(psd);
        List<OrganizacionSocial> list = new ArrayList<OrganizacionSocial>();
        list.add(organizacionSocial);
        reporte.setSolicitantes(list);
        //Campañia
        reportes.add(reporte);
        campania.setJornada(jornadas);
        campania.setBarrio(barrio);
        campania.setNombre("Campania");
        campania.setFechaFin(new Date());
        campania.setFechaInicio(new Date());
        campania.setReportes(reportes);
        campania.setEncuestadores(listEncuestadores);
        jornada.setZonaList(zonas);
        campaniaDAO.agregar(campania);
    }
    public void doPut(HttpServletRequest request, HttpServletResponse response){
        // Se asume que el usuario ya hizo uso del doPost() para agregar la camapña.
        Campania campania = campaniaDAO.buscarPorId(1);
        campania.setNombre("Campaña modificada");
        campaniaDAO.actualizar(campania);
        System.out.println(campania.getNombre()+" "+ campania.getFechaInicio());
    }
    public void doDelete(HttpServletRequest request, HttpServletResponse response){
        // Se asume que el usuario ya hizo uso del doPost() para agregar la camapña.
        Campania campania = campaniaDAO.buscarPorId(1);
        campaniaDAO.eliminar(campania.getId());
    }
}
