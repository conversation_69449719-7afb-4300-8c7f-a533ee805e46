package jyaa.trabajo_practico_3.Modelo.Encuesta;

import jakarta.persistence.*;

import java.util.List;

@Entity
// La ídea es que la pregunta sea generica y que la respuesta puede guarda distinto tipos de datos según convenga la situación
public class Pregunta {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Integer numero;
    private String pregunta;
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Seccion seccion;
    private String respuesta;
    private boolean respondio;
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RamasDeTrabajo> ramasDeTrabajo;
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProgramaDeEstado> programaDeEstado;
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CoberturaDeSalud> coberturaDeSalud;
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Vacuna>  vacunas;
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Sustancia> sustancias;
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Discapacidad> discapacidad;
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ControlDeEmbarazo> controlDeEmbarazos;

    public Integer getNumero() {
        return numero;
    }


    public void setNumero(Integer numero) {
        this.numero = numero;
    }

    public Seccion getSeccion() {
        return seccion;
    }

    public void setSeccion(Seccion seccion) {
        this.seccion = seccion;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPregunta() {
        return pregunta;
    }

    public void setPregunta(String pregunta) {
        this.pregunta = pregunta;
    }

    public String getRespuesta() {
        return respuesta;
    }

    public void setRespuesta(String respuesta) {
        this.respuesta = respuesta;
    }

    public boolean isRespondio() {
        return respondio;
    }

    public void setRespondio(boolean respondio) {
        this.respondio = respondio;
    }

    public List<RamasDeTrabajo> getRamasDeTrabajo() {
        return ramasDeTrabajo;
    }

    public void setRamasDeTrabajo(List<RamasDeTrabajo> ramasDeTrabajo) {
        this.ramasDeTrabajo = ramasDeTrabajo;
    }

    public List<ProgramaDeEstado> getProgramaDeEstado() {
        return programaDeEstado;
    }

    public void setProgramaDeEstado(List<ProgramaDeEstado> programaDeEstado) {
        this.programaDeEstado = programaDeEstado;
    }

    public List<CoberturaDeSalud> getCoberturaDeSalud() {
        return coberturaDeSalud;
    }

    public void setCoberturaDeSalud(List<CoberturaDeSalud> coberturaDeSalud) {
        this.coberturaDeSalud = coberturaDeSalud;
    }

    public List<Vacuna> getVacunas() {
        return vacunas;
    }

    public void setVacunas(List<Vacuna> vacunas) {
        this.vacunas = vacunas;
    }

    public List<Sustancia> getSustancias() {
        return sustancias;
    }

    public void setSustancias(List<Sustancia> sustancias) {
        this.sustancias = sustancias;
    }

    public List<Discapacidad> getDiscapacidad() {
        return discapacidad;
    }

    public void setDiscapacidad(List<Discapacidad> discapacidad) {
        this.discapacidad = discapacidad;
    }

    public List<ControlDeEmbarazo> getControlDeEmbarazos() {
        return controlDeEmbarazos;
    }

    public void setControlDeEmbarazos(List<ControlDeEmbarazo> controlDeEmbarazos) {
        this.controlDeEmbarazos = controlDeEmbarazos;
    }
}
