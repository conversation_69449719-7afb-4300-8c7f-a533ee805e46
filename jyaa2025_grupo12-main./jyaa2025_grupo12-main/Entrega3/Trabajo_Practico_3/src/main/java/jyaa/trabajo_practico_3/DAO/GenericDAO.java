package jyaa.trabajo_practico_3.DAO;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityTransaction;
import jakarta.persistence.NoResultException;
import jakarta.persistence.TypedQuery;
import jakarta.transaction.Transactional;
import jyaa.trabajo_practico_3.Util.ManagerFactory;

import java.util.List;
import java.util.Optional;

public  abstract class GenericDAO<T> implements GenericDAOInterface<T> {

    private Class<T> entityClass;
    public GenericDAO(Class<T> entityClass) {
        this.entityClass = entityClass;
    }
    @Override
    public List<T> listar() {
        EntityManager em = ManagerFactory.getEntityManager();
        String jpql = "select t from " + entityClass.getName() + " t ";
        TypedQuery<T> query = em.createQuery(jpql, entityClass);
        List<T> todasEntradas = query.getResultList();
        return todasEntradas;
    }

    @Override
    public T buscarPorId(long id) {
        try{
            EntityManager em = ManagerFactory.getEntityManager();
            if(id == 0) {
                return null;
            }
            String jpql = "select t from " + entityClass.getName() + " t where t.id = :id";
            TypedQuery<T> query = em.createQuery(jpql, entityClass);
            query.setParameter("id", id);
            T  unaEntrada= query.getSingleResult();
            return unaEntrada;
        }catch(NoResultException ex){
            System.out.println("No se encontro el id del objeto "+id);
            return null;
        }
    }


    @Override
    @Transactional
    public T actualizar(T entidad) {
        EntityManager em = ManagerFactory.getEntityManager();
        EntityTransaction tx = em.getTransaction();
        tx.begin();
        em.merge(entidad);
        tx.commit();
        return entidad;
        }
    @Transactional
    @Override
    public boolean eliminar( long id) {
        EntityManager em = ManagerFactory.getEntityManager();
        if (id == 0) {
            return false;
        } else {
            T u = this.buscarPorId(id);
            if (u == null) {
                return false;
            }
            EntityTransaction tx = em.getTransaction();
            tx.begin();
            em.remove(u);
            tx.commit();
            return true;
        }
    }
    @Override
    @Transactional
    public Optional<T> agregar(T u)  {
        EntityManager em = ManagerFactory.getEntityManager();
        EntityTransaction tx = em.getTransaction();
        tx.begin();
        em.persist(u);
        tx.commit();
        Optional<T> result = Optional.of(u);
       return result;
    }
}
