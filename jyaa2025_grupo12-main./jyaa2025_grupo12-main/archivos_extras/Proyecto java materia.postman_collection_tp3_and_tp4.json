{"info": {"_postman_id": "ea988198-75f6-492b-b402-baa86fd2f106", "name": "Proyecto java materia", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "30071283"}, "item": [{"name": "Trabajo_Practico_4", "item": [{"name": "Usuarios", "item": [{"name": "<PERSON><PERSON>rio <PERSON>mini<PERSON>", "item": [{"name": "Habilitacion", "item": [{"name": "Habilitar <PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"habilitacion\":{\n        \"activo\":true,\n        \"verificado\":true\n    }\n         \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/usuario/habilitar", "host": ["{{url_tp4}}"], "path": ["usuario", "habilitar"]}}, "response": []}, {"name": "Actualizar Habilitacion", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":2,\n    \"activo\":false,\n     \"verificado\":false\n}\n         ", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/usuario/habilitar", "host": ["{{url_tp4}}"], "path": ["usuario", "habilitar"]}}, "response": []}, {"name": "Listar habilitaciones", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":2,\n    \"activo\":false,\n     \"verificado\":false\n}\n         "}, "url": {"raw": "{{url_tp4}}/usuario/habilitar", "host": ["{{url_tp4}}"], "path": ["usuario", "habilitar"]}}, "response": []}, {"name": "Obtener Habilitacion", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":2,\n    \"activo\":false,\n     \"verificado\":false\n}\n         "}, "url": {"raw": "{{url_tp4}}/usuario/habilitar/one?id=1", "host": ["{{url_tp4}}"], "path": ["usuario", "habilitar", "one"], "query": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "Eliminar Habilitación", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp4}}/usuario/habilitar?id=1", "host": ["{{url_tp4}}"], "path": ["usuario", "habilitar"], "query": [{"key": "id", "value": "1"}]}}, "response": []}]}, {"name": "Agregar", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"nombre\":\"<PERSON><PERSON><PERSON><PERSON>\",\n    \"apellido\":\"<PERSON><PERSON><PERSON>\",\n    \"email\":\"se<PERSON><PERSON><PERSON><PERSON>@gmail.com\",\n    \"DNI\":40895648,\n    \"telefono\":\"2241506860\",\n    \"rol\":\"ADMINISTRADOR\",\n    \"habilitacion\":{\n        \"activo\":true,\n        \"verificado\":true\n    }     \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/usuario", "host": ["{{url_tp4}}"], "path": ["usuario"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n        \"id\":1,\n         \"nombre\":\"<PERSON><PERSON><PERSON><PERSON>\",\n         \"apellido\":\"<PERSON><PERSON><PERSON>\",\n         \"email\":\"seba<PERSON><PERSON><PERSON>@gmail.com\",\n         \"DNI\":40895648,\n         \"telefono\":\"2241506860\",\n         \"rol\":\"ADMINISTRADOR\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/usuario", "host": ["{{url_tp4}}"], "path": ["usuario"]}}, "response": []}, {"name": "Listar usuarios", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/usuario", "host": ["{{url_tp4}}"], "path": ["usuario"]}}, "response": []}, {"name": "Eliminar <PERSON><PERSON><PERSON>", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{url_tp4}}/usuario?id=2", "host": ["{{url_tp4}}"], "path": ["usuario"], "query": [{"key": "id", "value": "2"}]}}, "response": []}, {"name": "Obtener Usuario", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/usuario/one", "host": ["{{url_tp4}}"], "path": ["usuario", "one"]}}, "response": []}]}, {"name": "Usuario <PERSON>", "item": [{"name": "Agregar Organización Social", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"nombre\":\"<PERSON>epito\",\n    \"domicilio\":\"12 de noviembre\",\n    \"informacionAdicional\":\"Algo nuevo\",\n    \"actividadPrincipal\":\"Hacer pepitos\",\n    \"usuario\":{\n         \"nombre\":\"algo\",\n         \"apellido\":\"algo3\",\n         \"email\":\"<EMAIL>\",\n         \"DNI\":3929302,\n         \"telefono\":\"2245693209\",\n         \"rol\":\"REFERENTE\"\n        },\n    \"barrio\":{\n        \"nombre\":\"Barrio nuevo\",\n        \"informacionAdicional\":\"Es un barrio nuevo\",\n        \"zonas\":[\n            {\n                \"nombre\":\"Zona 1\",\n                \"informacion\":\"Es informacion de zona 1\",\n                \"coordenadas\":[\n                    {\n                    \"coorX\":1,\n                    \"coorY\":2\n                    },\n                    {\n                     \"coorX\":3,\n                     \"coorY\":4\n                    }\n                ]\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/organizacion_social", "host": ["{{url_tp4}}"], "path": ["organizacion_social"]}}, "response": []}, {"name": "Listar Organización Social", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/organizacion_social", "host": ["{{url_tp4}}"], "path": ["organizacion_social"]}}, "response": []}, {"name": "Editar Organización Social", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":1,\n    \"nombre\":\"<PERSON><PERSON><PERSON>\",\n    \"domicilio\":\"12 de noviembre\",\n    \"informacionAdicional\":\"Algo nuevo\",\n    \"actividadPrincipal\":\"Hacer pepitos\",\n    \"usuario\":{\n        \"id\":4,\n         \"nombre\":\"algo\",\n         \"apellido\":\"algo3\",\n         \"email\":\"<EMAIL>\",\n         \"DNI\":3929302,\n         \"telefono\":\"2245693209\",\n         \"rol\":\"REFERENTE\",\n         \"habilitacion\":{\n            \"activo\":false,\n            \"verificado\":false\n         }\n        },\n    \"barrio\":{\n        \"id\":1,\n        \"nombre\":\"Barrio nuevo\",\n        \"informacionAdicional\":\"Es un barrio nuevo\",\n        \"zonas\":[\n            {\n                \"id\":1,\n                \"nombre\":\"Zona 1\",\n                \"informacion\":\"Es informacion de zona 1\",\n                \"coordenadas\":[\n                    {\n                    \"id\":1,\n                    \"coorX\":1,\n                    \"coorY\":2\n                    },\n                    {\n                    \"id\":2,\n                     \"coorX\":3,\n                     \"coorY\":4\n                    }\n                ]\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/organizacion_social", "host": ["{{url_tp4}}"], "path": ["organizacion_social"]}}, "response": []}, {"name": "Obtener Organización Social", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{url_tp4}}/organizacion_social/one?id=1", "host": ["{{url_tp4}}"], "path": ["organizacion_social", "one"], "query": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "Eliminar Organización Social", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{url_tp4}}/organizacion_social?id=1", "host": ["{{url_tp4}}"], "path": ["organizacion_social"], "query": [{"key": "id", "value": "1"}]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Agregar encuestador", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"genero\":\"<PERSON><PERSON><PERSON><PERSON>\",\n    \"ocupacion\":\"E<PERSON>udiante\",\n    \"edad\":22,\n    \"usuario\":{\n         \"nombre\":\"Marciano\",\n         \"apellido\":\"Nuevo\",\n         \"email\":\"<EMAIL>\",\n         \"DNI\":11298342,\n         \"rol\":\"ENCUESTADOR\",\n         \"telefono\":\"3392339231\"\n        }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/encuestador", "host": ["{{url_tp4}}"], "path": ["encuestador"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":1,\n    \"genero\":\"<PERSON><PERSON><PERSON><PERSON>\",\n    \"ocupacion\":\"E<PERSON>udiante\",\n    \"edad\":25,\n    \"usuario\":{\n        \"id\":3,\n         \"nombre\":\"<PERSON>\",\n         \"apellido\":\"<PERSON><PERSON>\",\n         \"email\":\"<EMAIL>\",\n         \"DNI\":3929302,\n         \"rol\":\"ENCUESTADOR\",\n         \"telefono\":\"2249438221\",\n         \"habilitacion\":{\n            \"activo\":false,\n            \"verificado\":false\n         }\n        }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/encuestador", "host": ["{{url_tp4}}"], "path": ["encuestador"]}}, "response": []}, {"name": "Obtener encuestadores", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/encuestador", "host": ["{{url_tp4}}"], "path": ["encuestador"]}}, "response": []}, {"name": "Obtener Encuestador", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{url_tp4}}/encuestador/one?id=1", "host": ["{{url_tp4}}"], "path": ["encuestador", "one"], "query": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "Eliminar Encuestador", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp4}}/encuestador?id=1", "host": ["{{url_tp4}}"], "path": ["encuestador"], "query": [{"key": "id", "value": "1"}]}}, "response": []}]}, {"name": "Usuario Medico", "item": [{"name": "Agregar médico", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"matricula\":34582039485,\n    \"usuario\":{\n         \"rol\":\"MEDICO\",\n         \"telefono\":\"2241596432\",\n         \"nombre\":\"algo\",\n         \"apellido\":\"algo3\",\n         \"email\":\"<EMAIL>\",\n         \"DNI\":3929302\n        }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/medico", "host": ["{{url_tp4}}"], "path": ["medico"]}}, "response": []}, {"name": "Obtener Medico", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/medico/one?id=1", "host": ["{{url_tp4}}"], "path": ["medico", "one"], "query": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "Actualizar Medico", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":1,\n    \"matricula\":444444444,\n    \"usuario\":{\n        \"id\":5,\n         \"rol\":\"MEDICO\",\n         \"telefono\":\"2241596432\",\n         \"nombre\":\"Medico\",\n         \"apellido\":\"Nuevo\",\n         \"email\":\"<EMAIL>\",\n         \"DNI\":282120384,\n         \"habilitacion\":{\n            \"activo\":true,\n            \"verificado\":true\n         }\n        }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/medico", "host": ["{{url_tp4}}"], "path": ["medico"]}}, "response": []}, {"name": "Obtener Medicos", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{url_tp4}}/medico", "host": ["{{url_tp4}}"], "path": ["medico"]}}, "response": []}, {"name": "Eliminar Medico", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":1,\n    \"matricula\":444444444,\n    \"usuario\":{\n        \"id\":5,\n         \"rol\":\"MEDICO\",\n         \"telefono\":\"2241596432\",\n         \"nombre\":\"Medico\",\n         \"apellido\":\"Nuevo\",\n         \"email\":\"<EMAIL>\",\n         \"DNI\":282120384,\n         \"habilitacion\":{\n            \"activo\":true,\n            \"verificado\":true\n         }\n        }\n}"}, "url": {"raw": "{{url_tp4}}/medico?id=1", "host": ["{{url_tp4}}"], "path": ["medico"], "query": [{"key": "id", "value": "1"}]}}, "response": []}]}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Obtener Zonas", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/zonas", "host": ["{{url_tp4}}"], "path": ["zonas"]}}, "response": []}, {"name": "Agregar Zona", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"nombre\":\"La Boca\",\n    \"informacion\":\"El mejor lugar del mundo\",\n    \"coordenadas\":[\n        {\n            \"coorX\":\"-34\",\n            \"coorY\":\"29\"\n        },\n        {\n            \"coorX\":\"25\",\n            \"coorY\":\"15\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/zonas", "host": ["{{url_tp4}}"], "path": ["zonas"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":2,\n    \"nombre\":\"La Boca\",\n    \"informacion\":\"El mejor lugar del mundo\",\n    \"coordenadas\":[\n        {\n            \"coorX\":\"-34\",\n            \"coorY\":\"29\"\n        },\n        {\n            \"coorX\":\"25\",\n            \"coorY\":\"15\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/zonas", "host": ["{{url_tp4}}"], "path": ["zonas"]}}, "response": []}, {"name": "Obtener Zona", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{url_tp4}}/zonas/one?id=2", "host": ["{{url_tp4}}"], "path": ["zonas", "one"], "query": [{"key": "id", "value": "2"}]}}, "response": []}, {"name": "Eliminar Zona", "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":1,\n    \"genero\":\"<PERSON><PERSON><PERSON><PERSON>\",\n    \"ocupacion\":\"E<PERSON>udiante\",\n    \"edad\":25,\n    \"usuario\":{\n        \"id\":3,\n         \"nombre\":\"<PERSON>\",\n         \"apellido\":\"<PERSON><PERSON>\",\n         \"email\":\"<EMAIL>\",\n         \"DNI\":3929302,\n         \"rol\":\"ENCUESTADOR\",\n         \"telefono\":\"2249438221\",\n         \"habilitacion\":{\n            \"activo\":false,\n            \"verificado\":false\n         }\n        }\n}"}, "url": {"raw": "{{url_tp4}}/zonas?id=2", "host": ["{{url_tp4}}"], "path": ["zonas"], "query": [{"key": "id", "value": "2"}]}}, "response": []}]}, {"name": "Barrios", "item": [{"name": "Obtener Barrios", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/barrios/", "host": ["{{url_tp4}}"], "path": ["barrios", ""]}}, "response": []}, {"name": "Agregar <PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"nombre\":\"El Hueco\",\n    \"informacionAdicional\":\"Es un barrio muy loco\"\n\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/barrios", "host": ["{{url_tp4}}"], "path": ["barrios"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"nombre\":\"El Hueco\",\n    \"informacionAdicional\":\"Es un barrio muy loco\",\n    \"zonas\":[\n        {\n            \"id\":4\n        },\n        {\n            \"id\":5\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/barrios", "host": ["{{url_tp4}}"], "path": ["barrios"]}}, "response": []}, {"name": "Eliminar Barrio", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp4}}/barrios?id=4", "host": ["{{url_tp4}}"], "path": ["barrios"], "query": [{"key": "id", "value": "4"}]}}, "response": []}, {"name": "Obtener Barrio", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/barrios/one?id=3", "host": ["{{url_tp4}}"], "path": ["barrios", "one"], "query": [{"key": "id", "value": "3"}]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Obtener Jornadas", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/jornada", "host": ["{{url_tp4}}"], "path": ["jornada"]}}, "response": []}, {"name": "Agregar <PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"fecha\":\"2025-06-14 00:00:00\",\n    \"zonaList\":[\n        {\n            \"nombre\":\"Zona 1\",\n            \"informacion\":\"Es información de la zona 1\",\n            \"coordenadas\":[\n                {\n                    \"coorX\":\"39\",\n                    \"coorY\":\"20\"\n                }\n            ]\n        },\n        {\n            \"nombre\": \"Zona 2\",\n            \"informacion\": \"Es información de la zona 2\",\n            \"coordenadas\":[\n                {\n                    \"coorX\":\"34\",\n                    \"coorY\":\"25\"\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/jornada", "host": ["{{url_tp4}}"], "path": ["jornada"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\":1,\n    \"fecha\":\"2025-06-15 00:00:00\",\n    \"zonaList\":[\n        {\n            \"id\":1,\n            \"nombre\":\"Zona 1_2\",\n            \"informacion\":\"Es información de la zona 1\",\n            \"coordenadas\":[\n                {\n                    \"id\":1,\n                    \"coorX\":\"19\",\n                    \"coorY\":\"50\"\n                }\n            ]\n        },\n        {\n            \"id\":2,\n            \"nombre\": \"Zona 23\",\n            \"informacion\": \"Es información de la zona 2\",\n            \"coordenadas\":[\n                {\n                    \"id\":2,\n                    \"coorX\":\"34\",\n                    \"coorY\":\"25\"\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/jornada", "host": ["{{url_tp4}}"], "path": ["jornada"]}}, "response": []}, {"name": "Eliminar Jornada", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp4}}/jornada?id=1", "host": ["{{url_tp4}}"], "path": ["jornada"], "query": [{"key": "id", "value": "1"}]}}, "response": []}, {"name": "Obtener Jornada", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/jornada?id=1", "host": ["{{url_tp4}}"], "path": ["jornada"], "query": [{"key": "id", "value": "1"}]}}, "response": []}]}, {"name": "Campañas", "item": [{"name": "Obtener Campañas", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/campanias", "host": ["{{url_tp4}}"], "path": ["campanias"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    //Campaña\n    \"nombre\":\"Camapaña 1\",\n    \"fechaInicio\": \"2025-06-15 00:00:00\",\n    \"fechaFin\": \"2025-06-15 00:00:00\",\n    \"barrio\": {\n       //Barrio\n        \"nombre\":\"Barrio 1\",\n        \"informacionAdicional\": \"Informacion barrio 1\",\n        \"zonas\":[\n            {\n               \"nombre\":\"Zona 1\",\n               \"informacion\": \"Información de la zona 1\",\n               \"coordenadas\":[\n                {\n                    \"coorX\":\"-32.3\",\n                    \"coorY\":\"29.2\"\n                }\n               ] \n            }\n        ]\n    },\n    \"jornadas\":[\n       {\n        \"fecha\":\"2025-06-15 00:00:00\",\n        \"zonaList\":[\n            {\n                \"nombre\":\"Zona 2\",\n                \"informacion\":\"Información de la zona 2\",\n                \"coordenadas\":[\n                    {\n                        \"coorX\":\"-32.1\",\n                        \"coorY\":\"29.4\"\n                    }\n                ]\n            }\n        ]\n       } \n    ],\n    \"encuestadores\":[\n        {\n            \"genero\":\"Mas<PERSON>lino\",\n            \"ocupacion\":\"<PERSON>studiante\",\n            \"edad\":25,\n            \"usuario\":{\n                \"nombre\":\"<PERSON><PERSON>\",\n                \"apellido\":\"Cri\",\n                \"email\":\"<EMAIL>\",\n                \"DNI\":30493821,\n                \"telefono\":\"2249183482\",\n                \"rol\":\"ENCUESTADOR\"\n            }\n        }\n    ],\n    \"reportes\":[\n        {\n            \"nombre\":\"Reporte 1\",\n            \"imagen\":\"data://\",\n            \"publico\":true,\n            \"creador\":{\n                \"matricula\":2388332,\n                \"usuario\":{\n                    \"nombre\":\"Creador\",\n                    \"apellido\":\"Nuevo\",\n                    \"email\":\"<EMAIL>\",\n                    \"DNI\":39320384,\n                    \"telefono\":\"2291384321\",\n                    \"rol\":\"MEDICO\"\n                    \n                }\n            },\n            \"solicitantes\":[\n                {\n                   \"nombre\":\"Ayudante anonimos\",\n                    \"domicilio\":\"Domicilo nuevo 123\",\n                    \"informacionAdicional\":\"Es una organización que ayuda\",\n                    \"actividadPrincipal\":\"Hace lo que puede\",\n                    \"usuario\":{\n                        \"nombre\":\"trabajador 1\",\n                        \"apellido\":\"de la empresa\",\n                        \"DNI\":39420123,\n                        \"email\":\"<EMAIL>\",\n                        \"telefono\":\"238302383\",\n                        \"rol\":\"REFERENTE\"\n                        \n                    },\n                    \"barrio\":{\n                        \"nombre\":\"Barrio 5\",\n                        \"informacionAdicional\":\"Es una barrio\",\n                        \"zonas\":[\n                            {\n                                \"nombre\":\"Zona 3\",\n                \"informacion\":\"Información de la zona 3\",\n                \"coordenadas\":[\n                    {\n                        \"coorX\":\"-42.1\",\n                        \"coorY\":\"39.4\"\n                    }\n                ]\n                            }\n                        ]                       \n                    }\n                }\n            ]\n            \n        }\n\n    ],\n    \"encuestas\":[\n        {\n        \"preguntas\":[\n            {\n              \"numero\":1,\n                \"pregunta\":\"Quién sos?\",\n                \"respuesta\":\"Nadie\",\n                \"respondio\":true\n                \n            }\n        ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/campanias", "host": ["{{url_tp4}}"], "path": ["campanias"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    //Campaña\n    \"id\":1,\n    \"nombre\":\"Camapaña 1\",\n    \"fechaInicio\": \"2025-06-16 00:00:00\",\n    \"fechaFin\": \"2025-06-16 00:00:00\",\n    \"barrio\": {\n       //Barrio\n        \"id\":1,\n        \"nombre\":\"Barrio 1\",\n        \"informacionAdicional\": \"Informacion barrio 1\",\n        \"zonas\":[\n            {\n                \"id\":1,\n               \"nombre\":\"Zona 1\",\n               \"informacion\": \"Información de la zona 1\",\n               \"coordenadas\":[\n                {\n                    \"id\":1,\n                    \"coorX\":\"-32.3\",\n                    \"coorY\":\"29.2\"\n                }\n               ] \n            }\n        ]\n    },\n    \"jornadas\":[\n       {\n        \"id\":1,\n        \"fecha\":\"2025-06-15 00:00:00\",\n        \"zonaList\":[\n            {\n                \"id\":2,\n                \"nombre\":\"Zona 2\",\n                \"informacion\":\"Información de la zona 2\",\n                \"coordenadas\":[\n                    {\n                        \"id\":2,\n                        \"coorX\":\"-32.1\",\n                        \"coorY\":\"29.4\"\n                    }\n                ]\n            }\n        ]\n       } \n    ],\n    \"encuestadores\":[\n        {\n            \"id\":1,\n            \"genero\":\"Masculino\",\n            \"ocupacion\":\"Estudiante\",\n            \"edad\":25,\n            \"usuario\":{\n                \"id\":1,\n                \"nombre\":\"Marcelo\",\n                \"apellido\":\"Cri\",\n                \"email\":\"<EMAIL>\",\n                \"DNI\":30493821,\n                \"telefono\":\"2249183482\",\n                \"rol\":\"ENCUESTADOR\"\n            }\n        }\n    ],\n    \"reportes\":[\n        {\n            \"id\":1,\n            \"nombre\":\"Reporte 1\",\n            \"imagen\":\"data://\",\n            \"publico\":true,\n            \"creador\":{\n                \"id\":1,\n                \"matricula\":2388332,\n                \"usuario\":{\n                    \"id\":2,\n                    \"nombre\":\"Creador\",\n                    \"apellido\":\"Nuevo\",\n                    \"email\":\"<EMAIL>\",\n                    \"DNI\":39320384,\n                    \"telefono\":\"2291384321\",\n                    \"rol\":\"MEDICO\"\n                    \n                }\n            },\n            \"solicitantes\":[\n                {\n                    \"id\":1,\n                   \"nombre\":\"Ayudante anonimos\",\n                    \"domicilio\":\"Domicilo nuevo 123\",\n                    \"informacionAdicional\":\"Es una organización que ayuda\",\n                    \"actividadPrincipal\":\"Hace lo que puede\",\n                    \"usuario\":{\n                        \"id\":3,\n                        \"nombre\":\"trabajador 1\",\n                        \"apellido\":\"de la empresa\",\n                        \"DNI\":39420123,\n                        \"email\":\"<EMAIL>\",\n                        \"telefono\":\"238302383\",\n                        \"rol\":\"REFERENTE\"\n                        \n                    },\n                    \"barrio\":{\n                        \"id\":2,\n                        \"nombre\":\"Barrio 5\",\n                        \"informacionAdicional\":\"Es una barrio\",\n                        \"zonas\":[\n                            {\n                                \"id\":3,\n                                \"nombre\":\"Zona 3\",\n                                \"informacion\":\"Información de la zona 3\",\n                                \"coordenadas\":[\n                                    {\n                                        \"id\":3,\n                                        \"coorX\":\"-42.1\",\n                                        \"coorY\":\"39.4\"\n                                    }\n                                ]\n                            }\n                        ]                       \n                    }\n                }\n            ]\n            \n        }\n\n    ],\n    \"encuestas\":[\n        {\n            \"id\":1,\n        \"preguntas\":[\n            {\n                \"id\":1,\n                \"numero\":1,\n                \"pregunta\":\"Quién sos?\",\n                \"respuesta\":\"Nadie\",\n                \"respondio\":true\n            }\n        ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url_tp4}}/campanias", "host": ["{{url_tp4}}"], "path": ["campanias"]}}, "response": []}, {"name": "Obtener Campaña", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp4}}/campanias/one?id=2", "host": ["{{url_tp4}}"], "path": ["campanias", "one"], "query": [{"key": "id", "value": "2"}]}}, "response": []}, {"name": "Eliminar <PERSON>", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp4}}/campanias?id=2", "host": ["{{url_tp4}}"], "path": ["campanias"], "query": [{"key": "id", "value": "2"}]}}, "response": []}]}]}, {"name": "Trabajo_Practico_3", "item": [{"name": "Campaña", "item": [{"name": "Listar", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp3}}/ejemploC", "host": ["{{url_tp3}}"], "path": ["ejemploC"]}}, "response": []}, {"name": "Agregar Campania", "request": {"method": "POST", "header": [], "url": {"raw": "{{url_tp3}}/ejemploC", "host": ["{{url_tp3}}"], "path": ["ejemploC"]}}, "response": []}, {"name": "Modificar campaña", "request": {"method": "PUT", "header": [], "url": {"raw": "{{url_tp3}}/ejemploC", "host": ["{{url_tp3}}"], "path": ["ejemploC"]}}, "response": []}, {"name": "Eliminar <PERSON>", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp3}}/ejemploC", "host": ["{{url_tp3}}"], "path": ["ejemploC"]}}, "response": []}]}, {"name": "Encuesta", "item": [{"name": "Agregar Encuesta", "request": {"method": "POST", "header": [], "url": {"raw": "{{url_tp3}}/pregunta", "host": ["{{url_tp3}}"], "path": ["pregunta"]}}, "response": []}, {"name": "Listar Encuesta", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp3}}/pregunta", "host": ["{{url_tp3}}"], "path": ["pregunta"]}}, "response": []}, {"name": "Editar <PERSON>", "request": {"method": "PUT", "header": [], "url": {"raw": "{{url_tp3}}/pregunta", "host": ["{{url_tp3}}"], "path": ["pregunta"]}}, "response": []}, {"name": "Eliminar Encuesta", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp3}}/pregunta", "host": ["{{url_tp3}}"], "path": ["pregunta"]}}, "response": []}]}, {"name": "Filtro Mapa", "item": [{"name": "Agregar Filtro Mapa", "request": {"method": "POST", "header": [], "url": {"raw": "{{url_tp3}}/ejemploF", "host": ["{{url_tp3}}"], "path": ["ejemploF"]}}, "response": []}, {"name": "Listar Filtro Mapa", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp3}}/ejemploF", "host": ["{{url_tp3}}"], "path": ["ejemploF"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "url": {"raw": "{{url_tp3}}/ejemploF", "host": ["{{url_tp3}}"], "path": ["ejemploF"]}}, "response": []}, {"name": "Eliminar Filtro Mapa", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp3}}/ejemploF", "host": ["{{url_tp3}}"], "path": ["ejemploF"]}}, "response": []}]}, {"name": "Reporte", "item": [{"name": "Listar Reporte", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp3}}/ejemploR", "host": ["{{url_tp3}}"], "path": ["ejemploR"]}}, "response": []}, {"name": "Agregar Reporte", "request": {"method": "POST", "header": [], "url": {"raw": "{{url_tp3}}/ejemploR", "host": ["{{url_tp3}}"], "path": ["ejemploR"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "url": {"raw": "{{url_tp3}}/ejemploR", "host": ["{{url_tp3}}"], "path": ["ejemploR"]}}, "response": []}, {"name": "Eliminar Reporte", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp3}}/ejemploR", "host": ["{{url_tp3}}"], "path": ["ejemploR"]}}, "response": []}]}, {"name": "Usuario", "item": [{"name": "Listar Usuarios", "request": {"method": "GET", "header": [], "url": {"raw": "{{url_tp3}}/ejemploU", "host": ["{{url_tp3}}"], "path": ["ejemploU"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{url_tp3}}/ejemploU", "host": ["{{url_tp3}}"], "path": ["ejemploU"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [], "url": {"raw": "{{url_tp3}}/ejemploU", "host": ["{{url_tp3}}"], "path": ["ejemploU"]}}, "response": []}, {"name": "Eliminar Usua<PERSON>", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{url_tp3}}/ejemploU", "host": ["{{url_tp3}}"], "path": ["ejemploU"]}}, "response": []}]}]}]}