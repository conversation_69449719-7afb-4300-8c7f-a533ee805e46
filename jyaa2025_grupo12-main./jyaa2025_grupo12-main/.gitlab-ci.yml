buildDocker:
  stage: build
  tags:
    - jyaa
  only:
    - main
  script:
    - echo "<PERSON><PERSON><PERSON><PERSON><PERSON> la imagen de Docker"
    - docker buildx build . -t grupo12-docker

detenemosContainer:
  stage: test
  tags:
    - jyaa
  needs:
    - buildDocker
  only:
    - main
  script:
    - echo "Detenemos cualqueir tipo de contenedor existente previo"
    - docker stop grupo12 || true

eliminamosContainer:
  stage: test
  tags:
    - jyaa
  only:
    - main
  needs:
    - detenemosContainer
  script:
    - echo "Eliminamos cualqueir tipo de contenedor existente previo"
    - docker rm grupo12 || true

deploy1:
  stage: deploy
  tags:
    - jyaa
  needs:
    - eliminamosContainer
  only:
    - main
  script:
    - echo "Deploy del nuevo contenedor con los cambios"
    - docker run -p 8092:8092 -d --name grupo12 grupo12-docker
